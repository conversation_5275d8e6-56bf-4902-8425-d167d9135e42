const mongoose = require('mongoose');

const systemSettingsSchema = new mongoose.Schema({
  // Site configuration
  site: {
    name: {
      type: String,
      default: 'Wanderlust'
    },
    tagline: {
      type: String,
      default: 'Discover Your Next Adventure'
    },
    description: {
      type: String,
      default: 'Your premier destination for curated travel experiences'
    },
    logo: {
      public_id: String,
      url: String
    },
    favicon: {
      public_id: String,
      url: String
    },
    contactEmail: {
      type: String,
      default: '<EMAIL>'
    },
    supportEmail: {
      type: String,
      default: '<EMAIL>'
    },
    phone: String,
    address: {
      street: String,
      city: String,
      state: String,
      country: String,
      zipCode: String
    }
  },
  
  // Business settings
  business: {
    currency: {
      type: String,
      default: 'USD'
    },
    timezone: {
      type: String,
      default: 'UTC'
    },
    taxRate: {
      type: Number,
      default: 0,
      min: [0, 'Tax rate cannot be negative'],
      max: [100, 'Tax rate cannot exceed 100%']
    },
    serviceFee: {
      type: Number,
      default: 0,
      min: [0, 'Service fee cannot be negative']
    },
    cancellationWindow: {
      type: Number,
      default: 24, // hours
      min: [0, 'Cancellation window cannot be negative']
    },
    refundPolicy: {
      type: String,
      default: 'Full refund if cancelled 24 hours before tour start'
    }
  },
  
  // Email settings
  email: {
    fromName: {
      type: String,
      default: 'Wanderlust Team'
    },
    fromEmail: {
      type: String,
      default: '<EMAIL>'
    },
    templates: {
      welcome: {
        subject: {
          type: String,
          default: 'Welcome to Wanderlust!'
        },
        enabled: {
          type: Boolean,
          default: true
        }
      },
      bookingConfirmation: {
        subject: {
          type: String,
          default: 'Booking Confirmation - {{bookingId}}'
        },
        enabled: {
          type: Boolean,
          default: true
        }
      },
      bookingReminder: {
        subject: {
          type: String,
          default: 'Tour Reminder - {{tourName}}'
        },
        enabled: {
          type: Boolean,
          default: true
        },
        sendBefore: {
          type: Number,
          default: 24 // hours
        }
      },
      cancellation: {
        subject: {
          type: String,
          default: 'Booking Cancelled - {{bookingId}}'
        },
        enabled: {
          type: Boolean,
          default: true
        }
      }
    }
  },
  
  // Security settings
  security: {
    maxLoginAttempts: {
      type: Number,
      default: 5
    },
    lockoutDuration: {
      type: Number,
      default: 2 // hours
    },
    passwordMinLength: {
      type: Number,
      default: 6
    },
    requireEmailVerification: {
      type: Boolean,
      default: true
    },
    sessionTimeout: {
      type: Number,
      default: 24 // hours
    }
  },
  
  // Feature flags
  features: {
    userRegistration: {
      type: Boolean,
      default: true
    },
    guestBooking: {
      type: Boolean,
      default: false
    },
    reviewSystem: {
      type: Boolean,
      default: true
    },
    wishlist: {
      type: Boolean,
      default: true
    },
    notifications: {
      type: Boolean,
      default: true
    },
    multiLanguage: {
      type: Boolean,
      default: false
    },
    multiCurrency: {
      type: Boolean,
      default: false
    }
  },
  
  // Social media links
  socialMedia: {
    facebook: String,
    instagram: String,
    twitter: String,
    youtube: String,
    linkedin: String
  },
  
  // SEO settings
  seo: {
    metaTitle: {
      type: String,
      default: 'Wanderlust - Discover Your Next Adventure'
    },
    metaDescription: {
      type: String,
      default: 'Book amazing travel experiences with Wanderlust. Discover curated tours and adventures worldwide.'
    },
    metaKeywords: [String],
    googleAnalyticsId: String,
    facebookPixelId: String
  },
  
  // Maintenance mode
  maintenance: {
    enabled: {
      type: Boolean,
      default: false
    },
    message: {
      type: String,
      default: 'We are currently performing maintenance. Please check back soon.'
    },
    allowedIPs: [String] // IPs that can access during maintenance
  },
  
  // API settings
  api: {
    rateLimit: {
      windowMs: {
        type: Number,
        default: 15 * 60 * 1000 // 15 minutes
      },
      max: {
        type: Number,
        default: 100 // requests per window
      }
    },
    corsOrigins: [String]
  },
  
  // File upload settings
  uploads: {
    maxFileSize: {
      type: Number,
      default: 5 * 1024 * 1024 // 5MB
    },
    allowedImageTypes: {
      type: [String],
      default: ['image/jpeg', 'image/png', 'image/webp']
    },
    maxImagesPerTour: {
      type: Number,
      default: 10
    }
  },
  
  // Last updated info
  lastUpdatedBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Ensure only one settings document exists
systemSettingsSchema.index({}, { unique: true });

// Static method to get settings (creates default if none exist)
systemSettingsSchema.statics.getSettings = async function() {
  let settings = await this.findOne();
  
  if (!settings) {
    settings = await this.create({});
  }
  
  return settings;
};

// Static method to update settings
systemSettingsSchema.statics.updateSettings = async function(updates, updatedBy) {
  let settings = await this.findOne();
  
  if (!settings) {
    settings = new this(updates);
  } else {
    Object.assign(settings, updates);
  }
  
  settings.lastUpdatedBy = updatedBy;
  await settings.save();
  
  return settings;
};

module.exports = mongoose.model('SystemSettings', systemSettingsSchema);
