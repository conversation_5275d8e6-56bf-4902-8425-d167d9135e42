const express = require('express');
const router = express.Router();

// Import controllers
const {
  createBooking,
  getAllBookings,
  getBooking,
  updateBookingStatus,
  cancelBooking,
  getBookingStats,
  processBookingPayment,
  createBookingPaymentIntent,
  processBookingRefund,
  getBookingPaymentMethods
} = require('../controllers/bookingController');

const { protect, authorize } = require('../middleware/auth');

// All routes require authentication
router.use(protect);

// Payment methods
router.get('/payment-methods', getBookingPaymentMethods);

// Booking statistics (admin/tour-provider)
router.get('/stats', authorize('admin', 'tour-provider'), getBookingStats);

// Main booking routes
router.route('/')
  .get(authorize('admin', 'tour-provider'), getAllBookings)
  .post(createBooking);

router.route('/:id')
  .get(getBooking);

// Payment routes
router.post('/:id/payment', processBookingPayment);
router.post('/:id/payment-intent', createBookingPaymentIntent);
router.post('/:id/refund', authorize('admin', 'tour-provider'), processBookingRefund);

// Booking management
router.put('/:id/status', authorize('admin', 'tour-provider'), updateBookingStatus);
router.put('/:id/cancel', cancelBooking);

module.exports = router;
