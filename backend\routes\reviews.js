const express = require('express');
const router = express.Router({ mergeParams: true });

// Import controllers
const {
  getTourReviews,
  createReview,
  updateReview,
  deleteReview,
  markReviewHelpful,
  reportReview
} = require('../controllers/reviewController');

const { protect, authorize } = require('../middleware/auth');

// Routes for /api/tours/:tourId/reviews
router.route('/')
  .get(getTourReviews)
  .post(protect, createReview);

// Routes for /api/reviews/:id
router.use('/:id', protect);

router.route('/:id')
  .put(updateReview)
  .delete(deleteReview);

router.put('/:id/helpful', markReviewHelpful);
router.put('/:id/report', reportReview);

module.exports = router;
