const express = require('express');
const router = express.Router();

// Import controllers
const {
  getUserProfile,
  updateUserProfile,
  uploadProfileImage,
  getUserBookings,
  getUserBooking,
  cancelBooking,
  deleteAccount
} = require('../controllers/userController');

const { protect } = require('../middleware/auth');
const { uploadProfileImage: uploadMiddleware, handleMulterError } = require('../middleware/upload');

// All routes are protected
router.use(protect);

// Profile routes
router.route('/profile')
  .get(getUserProfile)
  .put(updateUserProfile);

router.post('/profile/image', uploadMiddleware, handleMulterError, uploadProfileImage);

// Booking routes
router.route('/bookings')
  .get(getUserBookings);

router.route('/bookings/:id')
  .get(getUserBooking);

router.put('/bookings/:id/cancel', cancelBooking);

// Account management
router.delete('/account', deleteAccount);

module.exports = router;
