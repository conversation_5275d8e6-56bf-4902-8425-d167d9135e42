@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Merriweather:wght@300;400;700&display=swap');

/* Tailwind CSS directives - Essential for Tailwind to work */
/* stylelint-disable-next-line at-rule-no-unknown */
@tailwind base;
/* stylelint-disable-next-line at-rule-no-unknown */
@tailwind components;
/* stylelint-disable-next-line at-rule-no-unknown */
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    background-color: white;
    color: #171717;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  * {
    border-color: #e5e5e5;
  }
}

@layer components {
  /* Button Components */
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.5rem;
    transition: all 0.2s;
    outline: none;
    border: none;
    cursor: pointer;
  }

  .btn:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn-primary {
    background-color: #2563eb;
    color: white;
  }

  .btn-primary:hover:not(:disabled) {
    background-color: #1d4ed8;
  }

  .btn-secondary {
    background-color: #f5f5f5;
    color: #171717;
  }

  .btn-secondary:hover:not(:disabled) {
    background-color: #e5e5e5;
  }

  .btn-outline {
    border: 1px solid #d4d4d4;
    background-color: white;
    color: #404040;
  }

  .btn-outline:hover:not(:disabled) {
    background-color: #fafafa;
  }

  .btn-ghost {
    background-color: transparent;
    color: #525252;
  }

  .btn-ghost:hover:not(:disabled) {
    background-color: #f5f5f5;
  }

  .btn-danger {
    background-color: #dc2626;
    color: white;
  }

  .btn-danger:hover:not(:disabled) {
    background-color: #b91c1c;
  }

  .btn-success {
    background-color: #059669;
    color: white;
  }

  .btn-success:hover:not(:disabled) {
    background-color: #047857;
  }

  .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }

  .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }

  /* Input Components */
  .input {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d4d4d4;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    font-size: 0.875rem;
    outline: none;
    transition: all 0.2s;
  }

  .input::placeholder {
    color: #a3a3a3;
  }

  .input:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.5);
    border-color: #2563eb;
  }

  .input-error {
    border-color: #fca5a5;
  }

  .input-error:focus {
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.5);
    border-color: #ef4444;
  }

  /* Card Components */
  .card {
    background-color: white;
    border-radius: 0.75rem;
    border: 1px solid #e5e5e5;
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .card-hover {
    background-color: white;
    border-radius: 0.75rem;
    border: 1px solid #e5e5e5;
    transition: all 0.2s;
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .card-hover:hover {
    transform: translateY(-0.25rem);
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Badge Components */
  .badge {
    display: inline-flex;
    align-items: center;
    padding: 0.125rem 0.625rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .badge-primary {
    background-color: #dbeafe;
    color: #1e40af;
  }

  .badge-success {
    background-color: #d1fae5;
    color: #065f46;
  }

  .badge-warning {
    background-color: #fef3c7;
    color: #92400e;
  }

  .badge-danger {
    background-color: #fee2e2;
    color: #991b1b;
  }

  .badge-neutral {
    background-color: #f5f5f5;
    color: #262626;
  }

  /* Loading Components */
  .spinner {
    animation: spin 1s linear infinite;
    border-radius: 9999px;
    border: 2px solid #e5e5e5;
    border-top-color: #2563eb;
  }

  /* Container Components */
  .container-custom {
    max-width: 80rem;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media (min-width: 640px) {
    .container-custom {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .container-custom {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  /* Text Components */
  .text-gradient {
    background: linear-gradient(to right, #2563eb, #ec4899);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }
}

@layer utilities {
  /* Custom scrollbar */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Glass effect */
  .glass {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Gradient backgrounds */
  .bg-gradient-primary {
    background: linear-gradient(to right, #3b82f6, #1d4ed8);
  }

  .bg-gradient-secondary {
    background: linear-gradient(to right, #ec4899, #be185d);
  }

  .bg-gradient-accent {
    background: linear-gradient(to right, #10b981, #047857);
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    line-clamp: 3;
  }
}

/* Keyframes for custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounceGentle {
  0%, 100% {
    transform: translateY(-5%);
  }
  50% {
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
