import React from 'react';

const LoadingSpinner = ({
  size = 'default',
  className = '',
  color = 'primary',
  ...props
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    default: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
  };

  const colorClasses = {
    primary: 'border-primary-600',
    white: 'border-white',
    neutral: 'border-neutral-600',
  };

  const classes = [
    'spinner',
    sizeClasses[size],
    colorClasses[color],
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={classes} {...props} />
  );
};

// Full page loading spinner
export const PageLoader = ({ message = 'Loading...' }) => (
  <div className="min-h-screen flex flex-col items-center justify-center">
    <LoadingSpinner size="xl" />
    <p className="mt-4 text-neutral-600">{message}</p>
  </div>
);

// Inline loading spinner
export const InlineLoader = ({ message, className = '' }) => (
  <div className={`flex items-center space-x-2 ${className}`}>
    <LoadingSpinner size="sm" />
    {message && <span className="text-sm text-neutral-600">{message}</span>}
  </div>
);

export default LoadingSpinner;
