const User = require('../models/User');
const Booking = require('../models/Booking');
const { uploadImage, deleteImage } = require('../config/cloudinary');

// @desc    Get user profile
// @route   GET /api/users/profile
// @access  Private
const getUserProfile = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Get user profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user profile'
    });
  }
};

// @desc    Update user profile
// @route   PUT /api/users/profile
// @access  Private
const updateUserProfile = async (req, res) => {
  try {
    const {
      firstName,
      lastName,
      phone,
      dateOfBirth,
      gender,
      address,
      preferences
    } = req.body;

    const updateData = {
      firstName,
      lastName,
      phone,
      dateOfBirth,
      gender,
      address,
      preferences
    };

    // Remove undefined fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key] === undefined) {
        delete updateData[key];
      }
    });

    const user = await User.findByIdAndUpdate(
      req.user.id,
      updateData,
      {
        new: true,
        runValidators: true
      }
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      data: user,
      message: 'Profile updated successfully'
    });
  } catch (error) {
    console.error('Update user profile error:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to update profile'
    });
  }
};

// @desc    Upload profile image
// @route   POST /api/users/profile/image
// @access  Private
const uploadProfileImage = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Please upload an image file'
      });
    }

    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Delete old image if exists
    if (user.profileImage && user.profileImage.public_id) {
      try {
        await deleteImage(user.profileImage.public_id);
      } catch (error) {
        console.log('Error deleting old profile image:', error);
      }
    }

    // Upload new image
    const result = await uploadImage(req.file.path, 'wanderlust/profiles');

    // Update user profile image
    user.profileImage = {
      public_id: result.public_id,
      url: result.url
    };

    await user.save();

    res.status(200).json({
      success: true,
      data: {
        profileImage: user.profileImage
      },
      message: 'Profile image uploaded successfully'
    });
  } catch (error) {
    console.error('Upload profile image error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload profile image'
    });
  }
};

// @desc    Get user bookings
// @route   GET /api/users/bookings
// @access  Private
const getUserBookings = async (req, res) => {
  try {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const skip = (page - 1) * limit;

    const { status, sort } = req.query;

    // Build query
    const query = { user: req.user.id };
    if (status) {
      query.status = status;
    }

    // Build sort
    let sortBy = { createdAt: -1 }; // Default: newest first
    if (sort) {
      switch (sort) {
        case 'oldest':
          sortBy = { createdAt: 1 };
          break;
        case 'tour-date-asc':
          sortBy = { 'tourDate.startDate': 1 };
          break;
        case 'tour-date-desc':
          sortBy = { 'tourDate.startDate': -1 };
          break;
        case 'amount-asc':
          sortBy = { 'pricing.totalAmount': 1 };
          break;
        case 'amount-desc':
          sortBy = { 'pricing.totalAmount': -1 };
          break;
      }
    }

    const bookings = await Booking.find(query)
      .populate({
        path: 'tour',
        select: 'name slug images destination duration category'
      })
      .sort(sortBy)
      .skip(skip)
      .limit(limit);

    const total = await Booking.countDocuments(query);
    const totalPages = Math.ceil(total / limit);

    res.status(200).json({
      success: true,
      data: bookings,
      pagination: {
        currentPage: page,
        totalPages,
        totalResults: total,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });
  } catch (error) {
    console.error('Get user bookings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user bookings'
    });
  }
};

// @desc    Get single booking
// @route   GET /api/users/bookings/:id
// @access  Private
const getUserBooking = async (req, res) => {
  try {
    const booking = await Booking.findOne({
      _id: req.params.id,
      user: req.user.id
    }).populate({
      path: 'tour',
      select: 'name slug images destination duration category provider',
      populate: {
        path: 'provider',
        select: 'firstName lastName companyInfo'
      }
    });

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    res.status(200).json({
      success: true,
      data: booking
    });
  } catch (error) {
    console.error('Get user booking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get booking'
    });
  }
};

// @desc    Cancel booking
// @route   PUT /api/users/bookings/:id/cancel
// @access  Private
const cancelBooking = async (req, res) => {
  try {
    const booking = await Booking.findOne({
      _id: req.params.id,
      user: req.user.id
    });

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Check if booking can be cancelled
    if (!booking.canCancel) {
      return res.status(400).json({
        success: false,
        message: 'This booking cannot be cancelled'
      });
    }

    // Cancel the booking
    await booking.cancelBooking(req.user.id, req.body.reason || 'Cancelled by user');

    res.status(200).json({
      success: true,
      data: booking,
      message: 'Booking cancelled successfully'
    });
  } catch (error) {
    console.error('Cancel booking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel booking'
    });
  }
};

// @desc    Delete user account
// @route   DELETE /api/users/account
// @access  Private
const deleteAccount = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check for active bookings
    const activeBookings = await Booking.countDocuments({
      user: req.user.id,
      status: { $in: ['confirmed', 'in-progress'] }
    });

    if (activeBookings > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete account with active bookings'
      });
    }

    // Deactivate account instead of deleting
    user.isActive = false;
    user.email = `deleted_${Date.now()}_${user.email}`;
    await user.save();

    res.status(200).json({
      success: true,
      message: 'Account deactivated successfully'
    });
  } catch (error) {
    console.error('Delete account error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete account'
    });
  }
};

module.exports = {
  getUserProfile,
  updateUserProfile,
  uploadProfileImage,
  getUserBookings,
  getUserBooking,
  cancelBooking,
  deleteAccount
};
