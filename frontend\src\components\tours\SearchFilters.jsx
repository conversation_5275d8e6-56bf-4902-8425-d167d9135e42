import React, { useState } from 'react';
import { Search, MapPin, Calendar, Users, DollarSign, Filter, X } from 'lucide-react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import Card from '../ui/Card';

const SearchFilters = ({ 
  filters, 
  onFiltersChange, 
  onSearch,
  isLoading = false,
  className = '' 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [localFilters, setLocalFilters] = useState({
    keyword: '',
    destination: '',
    startDate: '',
    endDate: '',
    guests: '',
    minPrice: '',
    maxPrice: '',
    category: '',
    difficulty: '',
    duration: '',
    ...filters
  });

  const categories = [
    'adventure',
    'cultural',
    'wildlife',
    'beach',
    'mountain',
    'city',
    'historical',
    'spiritual',
    'food',
    'photography',
    'luxury',
    'budget',
    'family',
    'solo',
    'group'
  ];

  const difficulties = ['easy', 'moderate', 'difficult', 'expert'];
  
  const durations = [
    { label: '1-3 days', value: '1-3' },
    { label: '4-7 days', value: '4-7' },
    { label: '8-14 days', value: '8-14' },
    { label: '15+ days', value: '15+' }
  ];

  const handleFilterChange = (key, value) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const handleSearch = () => {
    onSearch?.(localFilters);
  };

  const clearFilters = () => {
    const clearedFilters = {
      keyword: '',
      destination: '',
      startDate: '',
      endDate: '',
      guests: '',
      minPrice: '',
      maxPrice: '',
      category: '',
      difficulty: '',
      duration: ''
    };
    setLocalFilters(clearedFilters);
    onFiltersChange?.(clearedFilters);
  };

  const hasActiveFilters = Object.values(localFilters).some(value =>
    value !== '' && value !== null && value !== undefined
  );

  return (
    <Card className={`${className}`}>
      {/* Main Search Bar */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
        <div className="md:col-span-2">
          <Input
            placeholder="Search destinations, tours..."
            icon={Search}
            value={localFilters.keyword}
            onChange={(e) => handleFilterChange('keyword', e.target.value)}
          />
        </div>
        
        <div>
          <Input
            placeholder="Destination"
            icon={MapPin}
            value={localFilters.destination}
            onChange={(e) => handleFilterChange('destination', e.target.value)}
          />
        </div>
        
        <div>
          <Input
            type="date"
            placeholder="Start Date"
            icon={Calendar}
            value={localFilters.startDate}
            onChange={(e) => handleFilterChange('startDate', e.target.value)}
          />
        </div>
        
        <div className="flex gap-2">
          <select
            className="input flex-1"
            value={localFilters.guests}
            onChange={(e) => handleFilterChange('guests', e.target.value ? parseInt(e.target.value) : '')}
          >
            <option value="">Select Guests</option>
            {[1, 2, 3, 4, 5, 6, 7, 8].map(num => (
              <option key={num} value={num}>
                {num} Guest{num > 1 ? 's' : ''}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            icon={Filter}
            onClick={() => setIsExpanded(!isExpanded)}
          >
            More Filters
          </Button>
          
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              icon={X}
              onClick={clearFilters}
            >
              Clear All
            </Button>
          )}
        </div>
        
        <Button
          variant="primary"
          icon={Search}
          loading={isLoading}
          onClick={handleSearch}
        >
          Search Tours
        </Button>
      </div>

      {/* Expanded Filters */}
      {isExpanded && (
        <div className="mt-6 pt-6 border-t border-neutral-200 space-y-4">
          {/* Price Range */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Price Range
            </label>
            <div className="grid grid-cols-2 gap-4">
              <Input
                type="number"
                placeholder="Min Price"
                icon={DollarSign}
                value={localFilters.minPrice}
                onChange={(e) => handleFilterChange('minPrice', e.target.value)}
              />
              <Input
                type="number"
                placeholder="Max Price"
                icon={DollarSign}
                value={localFilters.maxPrice}
                onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
              />
            </div>
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Category
            </label>
            <select
              className="input w-full"
              value={localFilters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </option>
              ))}
            </select>
          </div>

          {/* Difficulty */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Difficulty Level
            </label>
            <select
              className="input w-full"
              value={localFilters.difficulty}
              onChange={(e) => handleFilterChange('difficulty', e.target.value)}
            >
              <option value="">Any Difficulty</option>
              {difficulties.map(difficulty => (
                <option key={difficulty} value={difficulty}>
                  {difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}
                </option>
              ))}
            </select>
          </div>

          {/* Duration */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Duration
            </label>
            <select
              className="input w-full"
              value={localFilters.duration}
              onChange={(e) => handleFilterChange('duration', e.target.value)}
            >
              <option value="">Any Duration</option>
              {durations.map(duration => (
                <option key={duration.value} value={duration.value}>
                  {duration.label}
                </option>
              ))}
            </select>
          </div>

          {/* Date Range */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Travel Dates
            </label>
            <div className="grid grid-cols-2 gap-4">
              <Input
                type="date"
                label="Start Date"
                value={localFilters.startDate}
                onChange={(e) => handleFilterChange('startDate', e.target.value)}
              />
              <Input
                type="date"
                label="End Date"
                value={localFilters.endDate}
                onChange={(e) => handleFilterChange('endDate', e.target.value)}
              />
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

export default SearchFilters;
