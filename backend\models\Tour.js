const mongoose = require('mongoose');

const tourSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Tour name is required'],
    trim: true,
    maxlength: [100, 'Tour name cannot be more than 100 characters']
  },
  slug: {
    type: String,
    unique: true
  },
  description: {
    type: String,
    required: [true, 'Tour description is required'],
    maxlength: [2000, 'Description cannot be more than 2000 characters']
  },
  shortDescription: {
    type: String,
    required: [true, 'Short description is required'],
    maxlength: [200, 'Short description cannot be more than 200 characters']
  },
  price: {
    type: Number,
    required: [true, 'Tour price is required'],
    min: [0, 'Price cannot be negative']
  },
  originalPrice: {
    type: Number,
    min: [0, 'Original price cannot be negative']
  },
  currency: {
    type: String,
    default: 'USD',
    enum: ['USD', 'EUR', 'GBP', 'INR', 'CAD', 'AUD']
  },
  duration: {
    days: {
      type: Number,
      required: [true, 'Duration in days is required'],
      min: [1, 'Duration must be at least 1 day']
    },
    nights: {
      type: Number,
      required: [true, 'Duration in nights is required'],
      min: [0, 'Nights cannot be negative']
    }
  },
  maxGroupSize: {
    type: Number,
    required: [true, 'Maximum group size is required'],
    min: [1, 'Group size must be at least 1'],
    max: [50, 'Group size cannot exceed 50']
  },
  difficulty: {
    type: String,
    required: [true, 'Tour difficulty is required'],
    enum: ['easy', 'moderate', 'difficult', 'expert']
  },
  category: {
    type: String,
    required: [true, 'Tour category is required'],
    enum: [
      'adventure',
      'cultural',
      'wildlife',
      'beach',
      'mountain',
      'city',
      'historical',
      'spiritual',
      'food',
      'photography',
      'luxury',
      'budget',
      'family',
      'solo',
      'group'
    ]
  },
  destination: {
    country: {
      type: String,
      required: [true, 'Country is required']
    },
    state: String,
    city: {
      type: String,
      required: [true, 'City is required']
    },
    coordinates: {
      latitude: {
        type: Number,
        min: [-90, 'Latitude must be between -90 and 90'],
        max: [90, 'Latitude must be between -90 and 90']
      },
      longitude: {
        type: Number,
        min: [-180, 'Longitude must be between -180 and 180'],
        max: [180, 'Longitude must be between -180 and 180']
      }
    }
  },
  images: [{
    public_id: {
      type: String,
      required: true
    },
    url: {
      type: String,
      required: true
    },
    caption: String,
    isMain: {
      type: Boolean,
      default: false
    }
  }],
  itinerary: [{
    day: {
      type: Number,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    },
    activities: [String],
    meals: {
      breakfast: { type: Boolean, default: false },
      lunch: { type: Boolean, default: false },
      dinner: { type: Boolean, default: false }
    },
    accommodation: String
  }],
  included: [String],
  excluded: [String],
  requirements: [String],
  whatToBring: [String],
  cancellationPolicy: {
    type: String,
    required: [true, 'Cancellation policy is required']
  },
  availableDates: [{
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date,
      required: true
    },
    availableSpots: {
      type: Number,
      required: true,
      min: [0, 'Available spots cannot be negative']
    },
    price: Number, // Can override base price for specific dates
    isActive: {
      type: Boolean,
      default: true
    }
  }],
  provider: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: [true, 'Tour must have a provider']
  },
  guides: [{
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  }],
  ratingsAverage: {
    type: Number,
    default: 0,
    min: [0, 'Rating must be above 0'],
    max: [5, 'Rating must be below 5.0'],
    set: val => Math.round(val * 10) / 10 // Round to 1 decimal place
  },
  ratingsQuantity: {
    type: Number,
    default: 0
  },
  totalBookings: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  tags: [String],
  seoTitle: String,
  seoDescription: String,
  seoKeywords: [String]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual populate for reviews
tourSchema.virtual('reviews', {
  ref: 'Review',
  foreignField: 'tour',
  localField: '_id'
});

// Virtual for discount percentage
tourSchema.virtual('discountPercentage').get(function() {
  if (this.originalPrice && this.originalPrice > this.price) {
    return Math.round(((this.originalPrice - this.price) / this.originalPrice) * 100);
  }
  return 0;
});

// Virtual for next available date
tourSchema.virtual('nextAvailableDate').get(function() {
  const now = new Date();
  const availableDates = this.availableDates
    .filter(date => date.startDate > now && date.isActive && date.availableSpots > 0)
    .sort((a, b) => a.startDate - b.startDate);
  
  return availableDates.length > 0 ? availableDates[0] : null;
});

// Indexes for better performance
tourSchema.index({ destination: 1 });
tourSchema.index({ category: 1 });
tourSchema.index({ price: 1 });
tourSchema.index({ ratingsAverage: -1 });
tourSchema.index({ createdAt: -1 });
tourSchema.index({ isActive: 1 });
tourSchema.index({ isFeatured: -1 });
tourSchema.index({ provider: 1 });
tourSchema.index({ slug: 1 });

// Text index for search functionality
tourSchema.index({
  name: 'text',
  description: 'text',
  'destination.country': 'text',
  'destination.city': 'text',
  category: 'text',
  tags: 'text'
});

// Pre-save middleware to generate slug
tourSchema.pre('save', function(next) {
  if (this.isModified('name') || this.isNew) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-zA-Z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }
  next();
});

// Pre-save middleware to ensure only one main image
tourSchema.pre('save', function(next) {
  if (this.isModified('images')) {
    let hasMain = false;
    this.images.forEach((image, index) => {
      if (image.isMain && !hasMain) {
        hasMain = true;
      } else if (image.isMain && hasMain) {
        this.images[index].isMain = false;
      }
    });
    
    // If no main image is set, make the first one main
    if (!hasMain && this.images.length > 0) {
      this.images[0].isMain = true;
    }
  }
  next();
});

module.exports = mongoose.model('Tour', tourSchema);
