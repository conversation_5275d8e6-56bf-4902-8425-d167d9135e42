const Booking = require('../models/Booking');
const Tour = require('../models/Tour');
const User = require('../models/User');
const APIFeatures = require('../utils/apiFeatures');
const {
  processPayment,
  processRefund,
  validatePaymentDetails,
  getPaymentMethods,
  createPaymentIntent
} = require('../utils/paymentProcessor');

// @desc    Create new booking
// @route   POST /api/bookings
// @access  Private
const createBooking = async (req, res) => {
  try {
    const {
      tourId,
      tourDate,
      participants,
      participantDetails,
      specialRequests
    } = req.body;

    // Get tour details
    const tour = await Tour.findById(tourId);
    if (!tour || !tour.isActive) {
      return res.status(404).json({
        success: false,
        message: 'Tour not found or not available'
      });
    }

    // Check if tour date is available
    const selectedDate = tour.availableDates.find(
      date => date.startDate.toISOString() === new Date(tourDate.startDate).toISOString()
    );

    if (!selectedDate || !selectedDate.isActive) {
      return res.status(400).json({
        success: false,
        message: 'Selected tour date is not available'
      });
    }

    // Check availability
    const totalParticipants = participants.adults + participants.children + participants.infants;
    if (selectedDate.availableSpots < totalParticipants) {
      return res.status(400).json({
        success: false,
        message: 'Not enough spots available for selected date'
      });
    }

    // Calculate pricing
    const basePrice = selectedDate.price || tour.price;
    const adultPrice = basePrice;
    const childPrice = basePrice * 0.7; // 30% discount for children
    const infantPrice = 0; // Infants are free

    const subtotal = (participants.adults * adultPrice) + 
                    (participants.children * childPrice) + 
                    (participants.infants * infantPrice);

    const taxes = subtotal * 0.1; // 10% tax
    const fees = 25; // Service fee
    const totalAmount = subtotal + taxes + fees;

    // Create booking
    const booking = await Booking.create({
      user: req.user.id,
      tour: tourId,
      tourDate: {
        startDate: tourDate.startDate,
        endDate: tourDate.endDate
      },
      participants,
      participantDetails,
      pricing: {
        basePrice,
        adultPrice,
        childPrice,
        infantPrice,
        subtotal,
        taxes,
        fees,
        totalAmount,
        currency: tour.currency
      },
      specialRequests,
      status: 'pending-payment'
    });

    // Update available spots
    selectedDate.availableSpots -= totalParticipants;
    await tour.save();

    // Populate booking with tour details
    await booking.populate({
      path: 'tour',
      select: 'name images destination duration provider',
      populate: {
        path: 'provider',
        select: 'firstName lastName companyInfo'
      }
    });

    res.status(201).json({
      success: true,
      data: booking,
      message: 'Booking created successfully'
    });
  } catch (error) {
    console.error('Create booking error:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to create booking'
    });
  }
};

// @desc    Get all bookings (admin/provider)
// @route   GET /api/bookings
// @access  Private (Admin/Tour Provider)
const getAllBookings = async (req, res) => {
  try {
    let query = {};

    // If tour provider, only show their bookings
    if (req.user.role === 'tour-provider') {
      const tours = await Tour.find({ provider: req.user.id }).select('_id');
      const tourIds = tours.map(tour => tour._id);
      query.tour = { $in: tourIds };
    }

    const features = new APIFeatures(Booking.find(query), req.query)
      .filter()
      .sort()
      .limitFields()
      .paginate();

    const bookings = await features.query
      .populate({
        path: 'user',
        select: 'firstName lastName email phone'
      })
      .populate({
        path: 'tour',
        select: 'name destination duration provider',
        populate: {
          path: 'provider',
          select: 'firstName lastName companyInfo'
        }
      });

    const paginationInfo = await features.getPaginationInfo();

    res.status(200).json({
      success: true,
      results: bookings.length,
      data: bookings,
      pagination: paginationInfo
    });
  } catch (error) {
    console.error('Get all bookings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get bookings'
    });
  }
};

// @desc    Get single booking
// @route   GET /api/bookings/:id
// @access  Private
const getBooking = async (req, res) => {
  try {
    let query = { _id: req.params.id };

    // If not admin, restrict access
    if (req.user.role !== 'admin') {
      if (req.user.role === 'user') {
        query.user = req.user.id;
      } else if (req.user.role === 'tour-provider') {
        const tours = await Tour.find({ provider: req.user.id }).select('_id');
        const tourIds = tours.map(tour => tour._id);
        query.tour = { $in: tourIds };
      }
    }

    const booking = await Booking.findOne(query)
      .populate({
        path: 'user',
        select: 'firstName lastName email phone profileImage'
      })
      .populate({
        path: 'tour',
        select: 'name slug images destination duration category provider itinerary',
        populate: {
          path: 'provider',
          select: 'firstName lastName companyInfo profileImage'
        }
      });

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    res.status(200).json({
      success: true,
      data: booking
    });
  } catch (error) {
    console.error('Get booking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get booking'
    });
  }
};

// @desc    Update booking status
// @route   PUT /api/bookings/:id/status
// @access  Private (Admin/Tour Provider)
const updateBookingStatus = async (req, res) => {
  try {
    const { status, internalNotes } = req.body;

    let query = { _id: req.params.id };

    // If tour provider, only allow updating their bookings
    if (req.user.role === 'tour-provider') {
      const tours = await Tour.find({ provider: req.user.id }).select('_id');
      const tourIds = tours.map(tour => tour._id);
      query.tour = { $in: tourIds };
    }

    const booking = await Booking.findOne(query);

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Validate status transition
    const validTransitions = {
      'pending-payment': ['confirmed', 'cancelled'],
      'confirmed': ['in-progress', 'cancelled', 'no-show'],
      'in-progress': ['completed', 'cancelled'],
      'completed': [],
      'cancelled': [],
      'no-show': [],
      'refunded': []
    };

    if (!validTransitions[booking.status].includes(status)) {
      return res.status(400).json({
        success: false,
        message: `Cannot change status from ${booking.status} to ${status}`
      });
    }

    booking.status = status;
    if (internalNotes) {
      booking.internalNotes = internalNotes;
    }

    // If confirming booking, update payment status
    if (status === 'confirmed' && booking.payment.status === 'pending') {
      booking.payment.status = 'completed';
      booking.payment.paidAt = new Date();
    }

    await booking.save();

    res.status(200).json({
      success: true,
      data: booking,
      message: 'Booking status updated successfully'
    });
  } catch (error) {
    console.error('Update booking status error:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to update booking status'
    });
  }
};

// @desc    Cancel booking
// @route   PUT /api/bookings/:id/cancel
// @access  Private
const cancelBooking = async (req, res) => {
  try {
    const { reason } = req.body;

    let query = { _id: req.params.id };

    // Users can only cancel their own bookings
    if (req.user.role === 'user') {
      query.user = req.user.id;
    } else if (req.user.role === 'tour-provider') {
      const tours = await Tour.find({ provider: req.user.id }).select('_id');
      const tourIds = tours.map(tour => tour._id);
      query.tour = { $in: tourIds };
    }

    const booking = await Booking.findOne(query);

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Check if booking can be cancelled
    if (!booking.canCancel) {
      return res.status(400).json({
        success: false,
        message: 'This booking cannot be cancelled'
      });
    }

    // Cancel the booking
    await booking.cancelBooking(req.user.id, reason);

    // Restore available spots
    const tour = await Tour.findById(booking.tour);
    const tourDate = tour.availableDates.find(
      date => date.startDate.toISOString() === booking.tourDate.startDate.toISOString()
    );
    
    if (tourDate) {
      tourDate.availableSpots += booking.totalParticipants;
      await tour.save();
    }

    res.status(200).json({
      success: true,
      data: booking,
      message: 'Booking cancelled successfully'
    });
  } catch (error) {
    console.error('Cancel booking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel booking'
    });
  }
};

// @desc    Get booking statistics
// @route   GET /api/bookings/stats
// @access  Private (Admin/Tour Provider)
const getBookingStats = async (req, res) => {
  try {
    let matchQuery = {};

    // If tour provider, only show their stats
    if (req.user.role === 'tour-provider') {
      const tours = await Tour.find({ provider: req.user.id }).select('_id');
      const tourIds = tours.map(tour => tour._id);
      matchQuery.tour = { $in: tourIds };
    }

    const stats = await Booking.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalRevenue: { $sum: '$pricing.totalAmount' },
          avgBookingValue: { $avg: '$pricing.totalAmount' }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Get monthly booking trends
    const monthlyStats = await Booking.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          bookings: { $sum: 1 },
          revenue: { $sum: '$pricing.totalAmount' }
        }
      },
      {
        $sort: { '_id.year': -1, '_id.month': -1 }
      },
      {
        $limit: 12
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        statusStats: stats,
        monthlyTrends: monthlyStats
      }
    });
  } catch (error) {
    console.error('Get booking stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get booking statistics'
    });
  }
};

// @desc    Process payment for booking
// @route   POST /api/bookings/:id/payment
// @access  Private
const processBookingPayment = async (req, res) => {
  try {
    const { paymentDetails } = req.body;

    // Validate payment details
    const validation = validatePaymentDetails(paymentDetails);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        message: validation.message
      });
    }

    const booking = await Booking.findOne({
      _id: req.params.id,
      user: req.user.id,
      status: 'pending-payment'
    });

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found or payment already processed'
      });
    }

    // Process payment
    const paymentResult = await processPayment(booking._id, paymentDetails);

    if (paymentResult.success) {
      // Send confirmation email (implement email service)
      await booking.sendConfirmation();

      res.status(200).json({
        success: true,
        data: {
          transactionId: paymentResult.transactionId,
          booking: booking
        },
        message: 'Payment processed successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        message: paymentResult.message
      });
    }
  } catch (error) {
    console.error('Process payment error:', error);
    res.status(500).json({
      success: false,
      message: 'Payment processing failed'
    });
  }
};

// @desc    Create payment intent
// @route   POST /api/bookings/:id/payment-intent
// @access  Private
const createBookingPaymentIntent = async (req, res) => {
  try {
    const booking = await Booking.findOne({
      _id: req.params.id,
      user: req.user.id,
      status: 'pending-payment'
    });

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found or payment already processed'
      });
    }

    const paymentIntent = await createPaymentIntent(
      booking._id,
      booking.pricing.totalAmount,
      booking.pricing.currency
    );

    res.status(200).json({
      success: true,
      data: paymentIntent
    });
  } catch (error) {
    console.error('Create payment intent error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create payment intent'
    });
  }
};

// @desc    Process refund
// @route   POST /api/bookings/:id/refund
// @access  Private (Admin/Tour Provider)
const processBookingRefund = async (req, res) => {
  try {
    const { refundAmount, reason } = req.body;

    let query = { _id: req.params.id };

    // If tour provider, only allow refunding their bookings
    if (req.user.role === 'tour-provider') {
      const tours = await Tour.find({ provider: req.user.id }).select('_id');
      const tourIds = tours.map(tour => tour._id);
      query.tour = { $in: tourIds };
    }

    const booking = await Booking.findOne(query);

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    if (booking.payment.status !== 'completed') {
      return res.status(400).json({
        success: false,
        message: 'Cannot refund unpaid booking'
      });
    }

    // Process refund
    const refundResult = await processRefund(booking._id, refundAmount, reason);

    if (refundResult.success) {
      res.status(200).json({
        success: true,
        data: {
          refundAmount: refundResult.refundAmount,
          booking: booking
        },
        message: 'Refund processed successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        message: refundResult.message
      });
    }
  } catch (error) {
    console.error('Process refund error:', error);
    res.status(500).json({
      success: false,
      message: 'Refund processing failed'
    });
  }
};

// @desc    Get payment methods
// @route   GET /api/bookings/payment-methods
// @access  Private
const getBookingPaymentMethods = async (req, res) => {
  try {
    const paymentMethods = getPaymentMethods();

    res.status(200).json({
      success: true,
      data: paymentMethods
    });
  } catch (error) {
    console.error('Get payment methods error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get payment methods'
    });
  }
};

module.exports = {
  createBooking,
  getAllBookings,
  getBooking,
  updateBookingStatus,
  cancelBooking,
  getBookingStats,
  processBookingPayment,
  createBookingPaymentIntent,
  processBookingRefund,
  getBookingPaymentMethods
};
