const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/wanderlust')
  .then(() => console.log('✅ Connected to MongoDB'))
  .catch((error) => console.error('❌ MongoDB connection error:', error));

const sampleUsers = [
  {
    firstName: 'John',
    lastName: 'Smith',
    email: '<EMAIL>',
    password: 'password123',
    role: 'tour-provider',
    isEmailVerified: true,
    companyInfo: {
      companyName: 'Adventure Tours Co.',
      description: 'Leading adventure tour provider with 10+ years experience',
      website: 'https://adventuretours.com',
      phone: '******-0123'
    }
  },
  {
    firstName: 'Sarah',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    isEmailVerified: true
  },
  {
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    password: 'user123',
    role: 'user',
    isEmailVerified: true
  }
];

const seedUsers = async () => {
  try {
    // Clear existing users
    await User.deleteMany({});
    console.log('🗑️  Cleared existing users');

    // Hash passwords and insert users
    const users = [];
    for (const userData of sampleUsers) {
      const hashedPassword = await bcrypt.hash(userData.password, 12);
      users.push({
        ...userData,
        password: hashedPassword
      });
    }

    const createdUsers = await User.insertMany(users);
    console.log(`✅ Successfully seeded ${createdUsers.length} users`);

    // Display created users
    createdUsers.forEach(user => {
      console.log(`   - ${user.firstName} ${user.lastName} (${user.email}) - ${user.role}`);
    });

    // Return the provider user ID for tour seeding
    const provider = createdUsers.find(user => user.role === 'tour-provider');
    console.log(`\n📝 Provider ID for tours: ${provider._id}`);

    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding users:', error);
    process.exit(1);
  }
};

seedUsers();
