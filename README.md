# Wanderlust 2.0 - Travel Booking Platform

A comprehensive travel booking platform that helps users explore and discover curated travel tours and experiences across various destinations.

## Features

- **User Authentication**: Secure login/signup with role-based access (Admin, User, Tour Provider)
- **Tour Discovery**: Browse and search through curated travel experiences
- **Dynamic Filtering**: Advanced search and filtering options
- **Personalized Recommendations**: AI-powered tour suggestions
- **Booking Management**: Complete booking flow with status tracking
- **Admin Dashboard**: Full administrative control panel
- **Tour Provider Portal**: Interface for tour operators to manage their offerings
- **Responsive Design**: Fully responsive across all devices

## Tech Stack

- **Frontend**: React.js, Vite, Tailwind CSS
- **Backend**: Node.js, Express.js
- **Database**: MongoDB
- **Authentication**: JWT (JSON Web Tokens)
- **File Upload**: Multer, Cloudinary
- **Version Control**: Git

## Project Structure

```
wanderlust2.0/
├── frontend/          # React frontend application
├── backend/           # Express.js backend API
├── docs/             # Documentation
└── README.md         # Project documentation
```

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- MongoDB
- Git

### Installation

1. Clone the repository
2. Install backend dependencies: `cd backend && npm install`
3. Install frontend dependencies: `cd frontend && npm install`
4. Set up environment variables
5. Start the development servers

## Development

- Backend runs on `http://localhost:5000`
- Frontend runs on `http://localhost:3000`

## Contributing

Please read our contributing guidelines before submitting pull requests.

## License

This project is licensed under the MIT License.
