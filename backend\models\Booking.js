const mongoose = require('mongoose');

const bookingSchema = new mongoose.Schema({
  bookingId: {
    type: String,
    unique: true,
    required: true
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: [true, 'Booking must belong to a user']
  },
  tour: {
    type: mongoose.Schema.ObjectId,
    ref: 'Tour',
    required: [true, 'Booking must be for a tour']
  },
  tourDate: {
    startDate: {
      type: Date,
      required: [true, 'Tour start date is required']
    },
    endDate: {
      type: Date,
      required: [true, 'Tour end date is required']
    }
  },
  participants: {
    adults: {
      type: Number,
      required: [true, 'Number of adults is required'],
      min: [1, 'At least one adult is required']
    },
    children: {
      type: Number,
      default: 0,
      min: [0, 'Number of children cannot be negative']
    },
    infants: {
      type: Number,
      default: 0,
      min: [0, 'Number of infants cannot be negative']
    }
  },
  participantDetails: [{
    firstName: {
      type: String,
      required: true,
      trim: true
    },
    lastName: {
      type: String,
      required: true,
      trim: true
    },
    dateOfBirth: Date,
    gender: {
      type: String,
      enum: ['male', 'female', 'other']
    },
    passportNumber: String,
    nationality: String,
    dietaryRequirements: String,
    medicalConditions: String,
    emergencyContact: {
      name: String,
      phone: String,
      relationship: String
    }
  }],
  pricing: {
    basePrice: {
      type: Number,
      required: true
    },
    adultPrice: {
      type: Number,
      required: true
    },
    childPrice: {
      type: Number,
      default: 0
    },
    infantPrice: {
      type: Number,
      default: 0
    },
    subtotal: {
      type: Number,
      required: true
    },
    taxes: {
      type: Number,
      default: 0
    },
    fees: {
      type: Number,
      default: 0
    },
    discount: {
      amount: {
        type: Number,
        default: 0
      },
      code: String,
      reason: String
    },
    totalAmount: {
      type: Number,
      required: true
    },
    currency: {
      type: String,
      default: 'USD'
    }
  },
  payment: {
    status: {
      type: String,
      enum: ['pending', 'processing', 'completed', 'failed', 'refunded', 'partially-refunded'],
      default: 'pending'
    },
    method: {
      type: String,
      enum: ['credit-card', 'debit-card', 'paypal', 'bank-transfer', 'cash']
    },
    transactionId: String,
    paymentIntentId: String, // For Stripe
    paidAt: Date,
    refundAmount: {
      type: Number,
      default: 0
    },
    refundReason: String,
    refundedAt: Date
  },
  status: {
    type: String,
    enum: [
      'pending-payment',
      'confirmed',
      'in-progress',
      'completed',
      'cancelled',
      'no-show',
      'refunded'
    ],
    default: 'pending-payment'
  },
  bookingSource: {
    type: String,
    enum: ['website', 'mobile-app', 'phone', 'email', 'walk-in'],
    default: 'website'
  },
  specialRequests: String,
  internalNotes: String, // For admin/provider use only
  confirmationSentAt: Date,
  reminderSentAt: Date,
  cancellation: {
    cancelledAt: Date,
    cancelledBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    reason: String,
    refundAmount: Number,
    cancellationFee: Number
  },
  review: {
    type: mongoose.Schema.ObjectId,
    ref: 'Review'
  },
  // Communication history
  communications: [{
    type: {
      type: String,
      enum: ['email', 'sms', 'phone', 'in-app']
    },
    subject: String,
    message: String,
    sentAt: {
      type: Date,
      default: Date.now
    },
    sentBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    status: {
      type: String,
      enum: ['sent', 'delivered', 'read', 'failed'],
      default: 'sent'
    }
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for total participants
bookingSchema.virtual('totalParticipants').get(function() {
  return this.participants.adults + this.participants.children + this.participants.infants;
});

// Virtual for booking duration
bookingSchema.virtual('duration').get(function() {
  if (this.tourDate.startDate && this.tourDate.endDate) {
    const diffTime = Math.abs(this.tourDate.endDate - this.tourDate.startDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  }
  return 0;
});

// Virtual for days until tour
bookingSchema.virtual('daysUntilTour').get(function() {
  if (this.tourDate.startDate) {
    const now = new Date();
    const diffTime = this.tourDate.startDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  }
  return 0;
});

// Virtual for can cancel
bookingSchema.virtual('canCancel').get(function() {
  const now = new Date();
  const tourStart = this.tourDate.startDate;
  const hoursUntilTour = (tourStart - now) / (1000 * 60 * 60);
  
  // Can cancel if tour is more than 24 hours away and status allows
  return hoursUntilTour > 24 && 
         ['pending-payment', 'confirmed'].includes(this.status) &&
         this.payment.status !== 'refunded';
});

// Indexes for better performance
bookingSchema.index({ user: 1 });
bookingSchema.index({ tour: 1 });
bookingSchema.index({ bookingId: 1 });
bookingSchema.index({ status: 1 });
bookingSchema.index({ 'payment.status': 1 });
bookingSchema.index({ 'tourDate.startDate': 1 });
bookingSchema.index({ createdAt: -1 });

// Pre-save middleware to generate booking ID
bookingSchema.pre('save', function(next) {
  if (this.isNew) {
    // Generate booking ID: WL + timestamp + random 4 digits
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(1000 + Math.random() * 9000);
    this.bookingId = `WL${timestamp}${random}`;
  }
  next();
});

// Pre-save middleware to calculate total amount
bookingSchema.pre('save', function(next) {
  if (this.isModified('pricing') || this.isNew) {
    const { adults, children, infants } = this.participants;
    const { adultPrice, childPrice, infantPrice, taxes, fees, discount } = this.pricing;
    
    this.pricing.subtotal = (adults * adultPrice) + (children * childPrice) + (infants * infantPrice);
    this.pricing.totalAmount = this.pricing.subtotal + taxes + fees - discount.amount;
  }
  next();
});

// Static method to get booking statistics
bookingSchema.statics.getBookingStats = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalRevenue: { $sum: '$pricing.totalAmount' }
      }
    }
  ]);
  
  return stats;
};

// Instance method to send confirmation
bookingSchema.methods.sendConfirmation = function() {
  // Implementation for sending booking confirmation
  this.confirmationSentAt = new Date();
  return this.save();
};

// Instance method to cancel booking
bookingSchema.methods.cancelBooking = function(cancelledBy, reason) {
  this.status = 'cancelled';
  this.cancellation = {
    cancelledAt: new Date(),
    cancelledBy: cancelledBy,
    reason: reason
  };
  
  return this.save();
};

module.exports = mongoose.model('Booking', bookingSchema);
