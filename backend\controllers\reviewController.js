const Review = require('../models/Review');
const Tour = require('../models/Tour');
const Booking = require('../models/Booking');
const APIFeatures = require('../utils/apiFeatures');

// @desc    Get all reviews for a tour
// @route   GET /api/tours/:tourId/reviews
// @access  Public
const getTourReviews = async (req, res) => {
  try {
    const { tourId } = req.params;
    
    const features = new APIFeatures(
      Review.find({ tour: tourId, isHidden: false })
        .populate('user', 'firstName lastName profileImage')
        .populate('booking', 'bookingDate')
        .sort('-createdAt'),
      req.query
    )
      .filter()
      .sort()
      .limitFields()
      .paginate();

    const reviews = await features.query;
    const total = await Review.countDocuments({ tour: tourId, isHidden: false });

    res.status(200).json({
      success: true,
      results: reviews.length,
      total,
      data: reviews
    });
  } catch (error) {
    console.error('Get tour reviews error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get reviews'
    });
  }
};

// @desc    Create a new review
// @route   POST /api/tours/:tourId/reviews
// @access  Private
const createReview = async (req, res) => {
  try {
    const { tourId } = req.params;
    const userId = req.user.id;
    const { review, rating, detailedRatings, isAnonymous } = req.body;

    // Check if tour exists
    const tour = await Tour.findById(tourId);
    if (!tour) {
      return res.status(404).json({
        success: false,
        message: 'Tour not found'
      });
    }

    // Check if user has booked this tour
    const booking = await Booking.findOne({
      user: userId,
      tour: tourId,
      status: 'confirmed'
    });

    if (!booking) {
      return res.status(400).json({
        success: false,
        message: 'You can only review tours you have booked'
      });
    }

    // Check if user has already reviewed this tour
    const existingReview = await Review.findOne({
      user: userId,
      tour: tourId,
      booking: booking._id
    });

    if (existingReview) {
      return res.status(400).json({
        success: false,
        message: 'You have already reviewed this tour'
      });
    }

    // Create review
    const newReview = await Review.create({
      review,
      rating,
      detailedRatings,
      tour: tourId,
      user: userId,
      booking: booking._id,
      isAnonymous: isAnonymous || false
    });

    // Populate the review
    await newReview.populate('user', 'firstName lastName profileImage');
    await newReview.populate('booking', 'bookingDate');

    res.status(201).json({
      success: true,
      data: newReview
    });
  } catch (error) {
    console.error('Create review error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create review'
    });
  }
};

// @desc    Update a review
// @route   PUT /api/reviews/:id
// @access  Private
const updateReview = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { review, rating, detailedRatings } = req.body;

    // Find review
    const existingReview = await Review.findById(id);
    if (!existingReview) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    // Check if user owns the review
    if (existingReview.user.toString() !== userId) {
      return res.status(403).json({
        success: false,
        message: 'You can only update your own reviews'
      });
    }

    // Update review
    const updatedReview = await Review.findByIdAndUpdate(
      id,
      { review, rating, detailedRatings },
      { new: true, runValidators: true }
    ).populate('user', 'firstName lastName profileImage');

    res.status(200).json({
      success: true,
      data: updatedReview
    });
  } catch (error) {
    console.error('Update review error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update review'
    });
  }
};

// @desc    Delete a review
// @route   DELETE /api/reviews/:id
// @access  Private
const deleteReview = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Find review
    const review = await Review.findById(id);
    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    // Check if user owns the review or is admin
    if (review.user.toString() !== userId && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'You can only delete your own reviews'
      });
    }

    await Review.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: 'Review deleted successfully'
    });
  } catch (error) {
    console.error('Delete review error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete review'
    });
  }
};

// @desc    Mark review as helpful
// @route   PUT /api/reviews/:id/helpful
// @access  Private
const markReviewHelpful = async (req, res) => {
  try {
    const { id } = req.params;

    const review = await Review.findById(id);
    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    await review.markHelpful();

    res.status(200).json({
      success: true,
      data: review
    });
  } catch (error) {
    console.error('Mark review helpful error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark review as helpful'
    });
  }
};

// @desc    Report a review
// @route   PUT /api/reviews/:id/report
// @access  Private
const reportReview = async (req, res) => {
  try {
    const { id } = req.params;

    const review = await Review.findById(id);
    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    await review.reportReview();

    res.status(200).json({
      success: true,
      message: 'Review reported successfully'
    });
  } catch (error) {
    console.error('Report review error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to report review'
    });
  }
};

module.exports = {
  getTourReviews,
  createReview,
  updateReview,
  deleteReview,
  markReviewHelpful,
  reportReview
};
