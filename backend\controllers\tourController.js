const Tour = require('../models/Tour');
const APIFeatures = require('../utils/apiFeatures');
const { uploadMultipleImages, deleteImage } = require('../config/cloudinary');
const {
  getPersonalizedRecommendations,
  getSimilarTours,
  getTrendingTours
} = require('../utils/tourRecommendations');

// @desc    Get all tours
// @route   GET /api/tours
// @access  Public
const getAllTours = async (req, res) => {
  try {
    // Build query
    const features = new APIFeatures(Tour.find({ isActive: true }), req.query)
      .filter()
      .search()
      .sort()
      .limitFields()
      .paginate();

    // Execute query
    const tours = await features.query.populate({
      path: 'provider',
      select: 'firstName lastName companyInfo'
    });

    // Get pagination info
    const paginationInfo = await features.getPaginationInfo();

    res.status(200).json({
      success: true,
      results: tours.length,
      data: tours,
      pagination: paginationInfo
    });
  } catch (error) {
    console.error('Get all tours error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get tours'
    });
  }
};

// @desc    Get single tour
// @route   GET /api/tours/:id
// @access  Public
const getTour = async (req, res) => {
  try {
    console.log('Getting tour with ID/slug:', req.params.id);

    let query = { isActive: true };

    // Check if the parameter is a valid ObjectId
    if (req.params.id.match(/^[0-9a-fA-F]{24}$/)) {
      // It's a valid ObjectId
      query._id = req.params.id;
    } else {
      // It's a slug
      query.slug = req.params.id;
    }

    const tour = await Tour.findOne(query)
    .populate({
      path: 'provider',
      select: 'firstName lastName companyInfo profileImage'
    });

    console.log('Found tour:', tour ? tour.name : 'Not found');

    if (!tour) {
      return res.status(404).json({
        success: false,
        message: 'Tour not found'
      });
    }

    res.status(200).json({
      success: true,
      data: tour
    });
  } catch (error) {
    console.error('Get tour error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get tour',
      error: error.message
    });
  }
};

// @desc    Create new tour
// @route   POST /api/tours
// @access  Private (Tour Provider/Admin)
const createTour = async (req, res) => {
  try {
    // Add provider to req.body
    req.body.provider = req.user.id;

    const tour = await Tour.create(req.body);

    res.status(201).json({
      success: true,
      data: tour,
      message: 'Tour created successfully'
    });
  } catch (error) {
    console.error('Create tour error:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to create tour'
    });
  }
};

// @desc    Update tour
// @route   PUT /api/tours/:id
// @access  Private (Tour Provider/Admin)
const updateTour = async (req, res) => {
  try {
    let tour = await Tour.findById(req.params.id);

    if (!tour) {
      return res.status(404).json({
        success: false,
        message: 'Tour not found'
      });
    }

    // Check if user owns the tour or is admin
    if (tour.provider.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this tour'
      });
    }

    tour = await Tour.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true
    });

    res.status(200).json({
      success: true,
      data: tour,
      message: 'Tour updated successfully'
    });
  } catch (error) {
    console.error('Update tour error:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to update tour'
    });
  }
};

// @desc    Delete tour
// @route   DELETE /api/tours/:id
// @access  Private (Tour Provider/Admin)
const deleteTour = async (req, res) => {
  try {
    const tour = await Tour.findById(req.params.id);

    if (!tour) {
      return res.status(404).json({
        success: false,
        message: 'Tour not found'
      });
    }

    // Check if user owns the tour or is admin
    if (tour.provider.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this tour'
      });
    }

    // Soft delete - just mark as inactive
    tour.isActive = false;
    await tour.save();

    res.status(200).json({
      success: true,
      message: 'Tour deleted successfully'
    });
  } catch (error) {
    console.error('Delete tour error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete tour'
    });
  }
};

// @desc    Upload tour images
// @route   POST /api/tours/:id/images
// @access  Private (Tour Provider/Admin)
const uploadTourImages = async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Please upload at least one image'
      });
    }

    const tour = await Tour.findById(req.params.id);

    if (!tour) {
      return res.status(404).json({
        success: false,
        message: 'Tour not found'
      });
    }

    // Check if user owns the tour or is admin
    if (tour.provider.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to upload images for this tour'
      });
    }

    // Upload images to Cloudinary
    const filePaths = req.files.map(file => file.path);
    const uploadResults = await uploadMultipleImages(filePaths, 'wanderlust/tours');

    // Add images to tour
    const newImages = uploadResults.map((result, index) => ({
      public_id: result.public_id,
      url: result.url,
      caption: req.body.captions ? req.body.captions[index] : '',
      isMain: tour.images.length === 0 && index === 0 // First image of empty tour becomes main
    }));

    tour.images.push(...newImages);
    await tour.save();

    res.status(200).json({
      success: true,
      data: {
        images: newImages
      },
      message: 'Images uploaded successfully'
    });
  } catch (error) {
    console.error('Upload tour images error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload images'
    });
  }
};

// @desc    Delete tour image
// @route   DELETE /api/tours/:id/images/:imageId
// @access  Private (Tour Provider/Admin)
const deleteTourImage = async (req, res) => {
  try {
    const tour = await Tour.findById(req.params.id);

    if (!tour) {
      return res.status(404).json({
        success: false,
        message: 'Tour not found'
      });
    }

    // Check if user owns the tour or is admin
    if (tour.provider.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete images from this tour'
      });
    }

    // Find image
    const imageIndex = tour.images.findIndex(
      img => img._id.toString() === req.params.imageId
    );

    if (imageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Image not found'
      });
    }

    const image = tour.images[imageIndex];

    // Delete from Cloudinary
    try {
      await deleteImage(image.public_id);
    } catch (error) {
      console.log('Error deleting image from Cloudinary:', error);
    }

    // Remove from tour
    tour.images.splice(imageIndex, 1);

    // If deleted image was main and there are other images, make first one main
    if (image.isMain && tour.images.length > 0) {
      tour.images[0].isMain = true;
    }

    await tour.save();

    res.status(200).json({
      success: true,
      message: 'Image deleted successfully'
    });
  } catch (error) {
    console.error('Delete tour image error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete image'
    });
  }
};

// @desc    Get featured tours
// @route   GET /api/tours/featured
// @access  Public
const getFeaturedTours = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit, 10) || 6;

    const tours = await Tour.find({
      isActive: true,
      isFeatured: true
    })
    .populate({
      path: 'provider',
      select: 'firstName lastName companyInfo'
    })
    .sort({ ratingsAverage: -1, createdAt: -1 })
    .limit(limit);

    res.status(200).json({
      success: true,
      results: tours.length,
      data: tours
    });
  } catch (error) {
    console.error('Get featured tours error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get featured tours'
    });
  }
};

// @desc    Get tour statistics
// @route   GET /api/tours/stats
// @access  Public
const getTourStats = async (req, res) => {
  try {
    const stats = await Tour.aggregate([
      {
        $match: { isActive: true }
      },
      {
        $group: {
          _id: '$category',
          numTours: { $sum: 1 },
          avgPrice: { $avg: '$price' },
          minPrice: { $min: '$price' },
          maxPrice: { $max: '$price' },
          avgRating: { $avg: '$ratingsAverage' }
        }
      },
      {
        $sort: { numTours: -1 }
      }
    ]);

    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get tour stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get tour statistics'
    });
  }
};

// @desc    Get personalized recommendations
// @route   GET /api/tours/recommendations
// @access  Private
const getRecommendations = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit, 10) || 10;
    const tours = await getPersonalizedRecommendations(req.user.id, limit);

    res.status(200).json({
      success: true,
      results: tours.length,
      data: tours
    });
  } catch (error) {
    console.error('Get recommendations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get recommendations'
    });
  }
};

// @desc    Get similar tours
// @route   GET /api/tours/:id/similar
// @access  Public
const getSimilarToursController = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit, 10) || 6;
    const tours = await getSimilarTours(req.params.id, limit);

    res.status(200).json({
      success: true,
      results: tours.length,
      data: tours
    });
  } catch (error) {
    console.error('Get similar tours error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get similar tours'
    });
  }
};

// @desc    Get trending tours
// @route   GET /api/tours/trending
// @access  Public
const getTrendingToursController = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit, 10) || 10;
    const tours = await getTrendingTours(limit);

    res.status(200).json({
      success: true,
      results: tours.length,
      data: tours
    });
  } catch (error) {
    console.error('Get trending tours error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get trending tours'
    });
  }
};

module.exports = {
  getAllTours,
  getTour,
  createTour,
  updateTour,
  deleteTour,
  uploadTourImages,
  deleteTourImage,
  getFeaturedTours,
  getTourStats,
  getRecommendations,
  getSimilarToursController,
  getTrendingToursController
};
