const Tour = require('../models/Tour');
const User = require('../models/User');
const Booking = require('../models/Booking');

// Get personalized tour recommendations for a user
const getPersonalizedRecommendations = async (userId, limit = 10) => {
  try {
    const user = await User.findById(userId);
    if (!user) {
      return await getPopularTours(limit);
    }

    // Get user's booking history
    const userBookings = await Booking.find({ 
      user: userId,
      status: { $in: ['completed', 'confirmed'] }
    }).populate('tour', 'category destination.country difficulty');

    // Extract preferences from user profile and booking history
    const preferences = extractUserPreferences(user, userBookings);

    // Build recommendation query
    const recommendationQuery = buildRecommendationQuery(preferences);

    // Get recommended tours
    const recommendedTours = await Tour.find(recommendationQuery)
      .populate({
        path: 'provider',
        select: 'firstName lastName companyInfo'
      })
      .sort({ 
        ratingsAverage: -1, 
        totalBookings: -1,
        createdAt: -1 
      })
      .limit(limit);

    // If not enough recommendations, fill with popular tours
    if (recommendedTours.length < limit) {
      const popularTours = await getPopularTours(limit - recommendedTours.length);
      const existingIds = recommendedTours.map(tour => tour._id.toString());
      const additionalTours = popularTours.filter(
        tour => !existingIds.includes(tour._id.toString())
      );
      recommendedTours.push(...additionalTours);
    }

    return recommendedTours;
  } catch (error) {
    console.error('Error getting personalized recommendations:', error);
    return await getPopularTours(limit);
  }
};

// Extract user preferences from profile and booking history
const extractUserPreferences = (user, bookings) => {
  const preferences = {
    categories: [],
    countries: [],
    difficulty: [],
    priceRange: { min: 0, max: Infinity }
  };

  // From user profile preferences
  if (user.preferences && user.preferences.travelPreferences) {
    const travelPrefs = user.preferences.travelPreferences;
    
    if (travelPrefs.preferredDestinations) {
      preferences.countries = travelPrefs.preferredDestinations;
    }
    
    if (travelPrefs.budgetRange) {
      preferences.priceRange = travelPrefs.budgetRange;
    }
  }

  // From booking history
  if (bookings && bookings.length > 0) {
    const categoryCount = {};
    const countryCount = {};
    const difficultyCount = {};

    bookings.forEach(booking => {
      if (booking.tour) {
        // Count categories
        if (booking.tour.category) {
          categoryCount[booking.tour.category] = (categoryCount[booking.tour.category] || 0) + 1;
        }
        
        // Count countries
        if (booking.tour.destination && booking.tour.destination.country) {
          countryCount[booking.tour.destination.country] = (countryCount[booking.tour.destination.country] || 0) + 1;
        }
        
        // Count difficulty levels
        if (booking.tour.difficulty) {
          difficultyCount[booking.tour.difficulty] = (difficultyCount[booking.tour.difficulty] || 0) + 1;
        }
      }
    });

    // Get top preferences from history
    preferences.categories = Object.keys(categoryCount)
      .sort((a, b) => categoryCount[b] - categoryCount[a])
      .slice(0, 3);
    
    preferences.countries = [...new Set([
      ...preferences.countries,
      ...Object.keys(countryCount)
        .sort((a, b) => countryCount[b] - countryCount[a])
        .slice(0, 3)
    ])];
    
    preferences.difficulty = Object.keys(difficultyCount)
      .sort((a, b) => difficultyCount[b] - difficultyCount[a])
      .slice(0, 2);
  }

  return preferences;
};

// Build MongoDB query for recommendations
const buildRecommendationQuery = (preferences) => {
  const query = {
    isActive: true,
    ratingsAverage: { $gte: 3.5 } // Only recommend well-rated tours
  };

  const orConditions = [];

  // Category preferences
  if (preferences.categories.length > 0) {
    orConditions.push({ category: { $in: preferences.categories } });
  }

  // Country preferences
  if (preferences.countries.length > 0) {
    orConditions.push({ 'destination.country': { $in: preferences.countries } });
  }

  // Difficulty preferences
  if (preferences.difficulty.length > 0) {
    orConditions.push({ difficulty: { $in: preferences.difficulty } });
  }

  // Price range
  if (preferences.priceRange.min > 0 || preferences.priceRange.max < Infinity) {
    query.price = {};
    if (preferences.priceRange.min > 0) {
      query.price.$gte = preferences.priceRange.min;
    }
    if (preferences.priceRange.max < Infinity) {
      query.price.$lte = preferences.priceRange.max;
    }
  }

  // Add OR conditions if any exist
  if (orConditions.length > 0) {
    query.$or = orConditions;
  }

  return query;
};

// Get popular tours (fallback for recommendations)
const getPopularTours = async (limit = 10) => {
  try {
    return await Tour.find({
      isActive: true,
      ratingsAverage: { $gte: 4.0 }
    })
    .populate({
      path: 'provider',
      select: 'firstName lastName companyInfo'
    })
    .sort({ 
      totalBookings: -1,
      ratingsAverage: -1,
      ratingsQuantity: -1
    })
    .limit(limit);
  } catch (error) {
    console.error('Error getting popular tours:', error);
    return [];
  }
};

// Get similar tours based on a specific tour
const getSimilarTours = async (tourId, limit = 6) => {
  try {
    const tour = await Tour.findById(tourId);
    if (!tour) {
      return [];
    }

    const similarTours = await Tour.find({
      _id: { $ne: tourId },
      isActive: true,
      $or: [
        { category: tour.category },
        { 'destination.country': tour.destination.country },
        { difficulty: tour.difficulty },
        { 
          price: { 
            $gte: tour.price * 0.7, 
            $lte: tour.price * 1.3 
          } 
        }
      ]
    })
    .populate({
      path: 'provider',
      select: 'firstName lastName companyInfo'
    })
    .sort({ 
      ratingsAverage: -1,
      totalBookings: -1
    })
    .limit(limit);

    return similarTours;
  } catch (error) {
    console.error('Error getting similar tours:', error);
    return [];
  }
};

// Get trending tours (recently popular)
const getTrendingTours = async (limit = 10) => {
  try {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Get tours with recent bookings
    const recentBookings = await Booking.aggregate([
      {
        $match: {
          createdAt: { $gte: thirtyDaysAgo },
          status: { $in: ['confirmed', 'completed'] }
        }
      },
      {
        $group: {
          _id: '$tour',
          bookingCount: { $sum: 1 }
        }
      },
      {
        $sort: { bookingCount: -1 }
      },
      {
        $limit: limit
      }
    ]);

    const tourIds = recentBookings.map(booking => booking._id);

    const trendingTours = await Tour.find({
      _id: { $in: tourIds },
      isActive: true
    })
    .populate({
      path: 'provider',
      select: 'firstName lastName companyInfo'
    });

    // Sort by recent booking count
    const sortedTours = trendingTours.sort((a, b) => {
      const aBookings = recentBookings.find(booking => 
        booking._id.toString() === a._id.toString()
      )?.bookingCount || 0;
      const bBookings = recentBookings.find(booking => 
        booking._id.toString() === b._id.toString()
      )?.bookingCount || 0;
      return bBookings - aBookings;
    });

    return sortedTours;
  } catch (error) {
    console.error('Error getting trending tours:', error);
    return await getPopularTours(limit);
  }
};

module.exports = {
  getPersonalizedRecommendations,
  getSimilarTours,
  getTrendingTours,
  getPopularTours
};
