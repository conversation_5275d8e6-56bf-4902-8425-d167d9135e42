import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Grid, List, SlidersHorizontal } from 'lucide-react';
import { tourAPI } from '../services/api';
import SearchFilters from '../components/tours/SearchFilters';
import TourCard from '../components/tours/TourCard';
import LoadingSpinner, { PageLoader } from '../components/ui/LoadingSpinner';
import Button from '../components/ui/Button';
import toast from 'react-hot-toast';

const ToursPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [tours, setTours] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchLoading, setSearchLoading] = useState(false);
  const [viewMode, setViewMode] = useState('grid');
  const [sortBy, setSortBy] = useState('featured');
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalResults: 0,
    hasNextPage: false,
    hasPrevPage: false
  });

  // Initialize filters from URL params
  const [filters, setFilters] = useState({
    keyword: searchParams.get('keyword') || '',
    destination: searchParams.get('destination') || '',
    category: searchParams.get('category') || '',
    minPrice: searchParams.get('minPrice') || '',
    maxPrice: searchParams.get('maxPrice') || '',
    difficulty: searchParams.get('difficulty') || '',
    duration: searchParams.get('duration') || '',
    startDate: searchParams.get('startDate') || '',
    endDate: searchParams.get('endDate') || '',
    guests: searchParams.get('guests') ? parseInt(searchParams.get('guests')) : '',
  });

  const sortOptions = [
    { value: 'featured', label: 'Featured First' },
    { value: 'price-asc', label: 'Price: Low to High' },
    { value: 'price-desc', label: 'Price: High to Low' },
    { value: 'rating', label: 'Highest Rated' },
    { value: 'newest', label: 'Newest First' },
    { value: 'popular', label: 'Most Popular' },
  ];

  // Fetch tours
  const fetchTours = async (page = 1, newFilters = filters, newSort = sortBy) => {
    try {
      setSearchLoading(true);

      const params = {
        page,
        limit: 12,
        sort: newSort,
        ...newFilters
      };

      // Clean up empty params
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key];
        }
      });

      const response = await tourAPI.getAllTours(params);
      const { data, pagination: paginationData } = response.data;

      setTours(data);
      setPagination(paginationData);

      // Update URL params
      const newSearchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value && key !== 'page' && key !== 'limit' && key !== 'sort') {
          newSearchParams.set(key, value.toString());
        }
      });
      setSearchParams(newSearchParams);

    } catch (error) {
      console.error('Error fetching tours:', error);
      toast.error('Failed to load tours');
    } finally {
      setLoading(false);
      setSearchLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchTours();
  }, []);

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleSearch = (searchFilters) => {
    setFilters(searchFilters);
    fetchTours(1, searchFilters, sortBy);
  };

  const handleSortChange = (newSort) => {
    setSortBy(newSort);
    fetchTours(pagination.currentPage, filters, newSort);
  };

  const handlePageChange = (page) => {
    fetchTours(page, filters, sortBy);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  if (loading) {
    return <PageLoader message="Loading amazing tours..." />;
  }

  return (
    <div className="min-h-screen bg-neutral-50">
      {/* Hero Section */}
      <div className="bg-gradient-primary text-white py-16">
        <div className="container-custom">
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Discover Amazing Tours
            </h1>
            <p className="text-xl text-neutral-200 max-w-2xl mx-auto">
              Explore {pagination.totalResults} carefully curated travel experiences around the world
            </p>
          </div>
        </div>
      </div>

      <div className="container-custom py-8">
        {/* Search Filters */}
        <div className="mb-8">
          <SearchFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onSearch={handleSearch}
            isLoading={searchLoading}
          />
        </div>

        {/* Results Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
          <div>
            <h2 className="text-2xl font-semibold text-neutral-900">
              {pagination.totalResults} Tours Found
            </h2>
            {filters.keyword && (
              <p className="text-neutral-600 mt-1">
                Results for "{filters.keyword}"
              </p>
            )}
          </div>

          <div className="flex items-center gap-4">
            {/* Sort Dropdown */}
            <select
              value={sortBy}
              onChange={(e) => handleSortChange(e.target.value)}
              className="input min-w-[200px]"
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>

            {/* View Mode Toggle */}
            <div className="flex border border-neutral-300 rounded-lg overflow-hidden">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 ${viewMode === 'grid'
                  ? 'bg-primary-600 text-white'
                  : 'bg-white text-neutral-600 hover:bg-neutral-50'
                }`}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 ${viewMode === 'list'
                  ? 'bg-primary-600 text-white'
                  : 'bg-white text-neutral-600 hover:bg-neutral-50'
                }`}
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Loading State */}
        {searchLoading && (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        )}

        {/* Tours Grid/List */}
        {!searchLoading && (
          <>
            {tours.length > 0 ? (
              <div className={`
                ${viewMode === 'grid'
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                  : 'space-y-6'
                }
              `}>
                {tours.map((tour) => (
                  <TourCard
                    key={tour._id}
                    tour={tour}
                    className={viewMode === 'list' ? 'flex' : ''}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-24 h-24 bg-neutral-200 rounded-full flex items-center justify-center mx-auto mb-4">
                  <SlidersHorizontal className="w-8 h-8 text-neutral-400" />
                </div>
                <h3 className="text-xl font-semibold text-neutral-900 mb-2">
                  No tours found
                </h3>
                <p className="text-neutral-600 mb-6">
                  Try adjusting your search criteria or browse all tours
                </p>
                <Button
                  variant="primary"
                  onClick={() => {
                    setFilters({
                      keyword: '',
                      destination: '',
                      category: '',
                      minPrice: '',
                      maxPrice: '',
                      difficulty: '',
                      duration: '',
                      startDate: '',
                      endDate: '',
                      guests: '',
                    });
                    fetchTours(1, {});
                  }}
                >
                  View All Tours
                </Button>
              </div>
            )}
          </>
        )}

        {/* Pagination */}
        {!searchLoading && tours.length > 0 && pagination.totalPages > 1 && (
          <div className="flex justify-center mt-12">
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                disabled={!pagination.hasPrevPage}
                onClick={() => handlePageChange(pagination.currentPage - 1)}
              >
                Previous
              </Button>

              {/* Page Numbers */}
              {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                const pageNum = Math.max(1, pagination.currentPage - 2) + i;
                if (pageNum > pagination.totalPages) return null;

                return (
                  <Button
                    key={pageNum}
                    variant={pageNum === pagination.currentPage ? 'primary' : 'outline'}
                    onClick={() => handlePageChange(pageNum)}
                  >
                    {pageNum}
                  </Button>
                );
              })}

              <Button
                variant="outline"
                disabled={!pagination.hasNextPage}
                onClick={() => handlePageChange(pagination.currentPage + 1)}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ToursPage;
