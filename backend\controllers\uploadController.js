const { uploadImage, uploadMultipleImages, deleteImage } = require('../config/cloudinary');
const fs = require('fs');
const path = require('path');

// @desc    Upload single image
// @route   POST /api/upload/image
// @access  Private
const uploadSingleImage = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Please upload an image file'
      });
    }

    // Upload to Cloudinary
    const result = await uploadImage(req.file.path, 'wanderlust/general');

    // Delete local file after upload
    fs.unlinkSync(req.file.path);

    res.status(200).json({
      success: true,
      data: {
        public_id: result.public_id,
        url: result.url,
        width: result.width,
        height: result.height
      },
      message: 'Image uploaded successfully'
    });
  } catch (error) {
    // Clean up local file if upload fails
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    console.error('Upload single image error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload image'
    });
  }
};

// @desc    Upload multiple images
// @route   POST /api/upload/images
// @access  Private
const uploadMultipleImagesController = async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Please upload at least one image'
      });
    }

    // Upload to Cloudinary
    const filePaths = req.files.map(file => file.path);
    const results = await uploadMultipleImages(filePaths, 'wanderlust/general');

    // Delete local files after upload
    req.files.forEach(file => {
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
    });

    res.status(200).json({
      success: true,
      data: results,
      message: 'Images uploaded successfully'
    });
  } catch (error) {
    // Clean up local files if upload fails
    if (req.files) {
      req.files.forEach(file => {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      });
    }

    console.error('Upload multiple images error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload images'
    });
  }
};

// @desc    Delete image
// @route   DELETE /api/upload/image/:publicId
// @access  Private
const deleteImageController = async (req, res) => {
  try {
    const { publicId } = req.params;

    if (!publicId) {
      return res.status(400).json({
        success: false,
        message: 'Public ID is required'
      });
    }

    // Delete from Cloudinary
    const result = await deleteImage(publicId);

    res.status(200).json({
      success: true,
      data: result,
      message: 'Image deleted successfully'
    });
  } catch (error) {
    console.error('Delete image error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete image'
    });
  }
};

// @desc    Get upload configuration
// @route   GET /api/upload/config
// @access  Private
const getUploadConfig = async (req, res) => {
  try {
    const config = {
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
      maxFiles: 10,
      supportedFormats: ['jpg', 'jpeg', 'png', 'webp', 'gif'],
      cloudinaryConfig: {
        cloudName: process.env.CLOUDINARY_CLOUD_NAME,
        uploadPreset: 'wanderlust_uploads' // You would create this in Cloudinary
      }
    };

    res.status(200).json({
      success: true,
      data: config
    });
  } catch (error) {
    console.error('Get upload config error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get upload configuration'
    });
  }
};

// @desc    Generate signed upload URL (for direct client uploads)
// @route   POST /api/upload/signed-url
// @access  Private
const generateSignedUploadUrl = async (req, res) => {
  try {
    const { folder = 'wanderlust/general', resourceType = 'image' } = req.body;

    // Generate timestamp and signature for Cloudinary
    const timestamp = Math.round(new Date().getTime() / 1000);
    const params = {
      timestamp,
      folder,
      resource_type: resourceType,
      transformation: 'c_limit,w_1200,h_800,q_auto,f_auto'
    };

    // In a real implementation, you would generate the signature using Cloudinary's SDK
    const signature = 'mock_signature_' + timestamp;

    res.status(200).json({
      success: true,
      data: {
        url: `https://api.cloudinary.com/v1_1/${process.env.CLOUDINARY_CLOUD_NAME}/${resourceType}/upload`,
        params: {
          ...params,
          api_key: process.env.CLOUDINARY_API_KEY,
          signature
        }
      }
    });
  } catch (error) {
    console.error('Generate signed URL error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate signed upload URL'
    });
  }
};

// @desc    Process image after upload (resize, optimize, etc.)
// @route   POST /api/upload/process
// @access  Private
const processImage = async (req, res) => {
  try {
    const { publicId, transformations } = req.body;

    if (!publicId) {
      return res.status(400).json({
        success: false,
        message: 'Public ID is required'
      });
    }

    // Apply transformations using Cloudinary
    const cloudinary = require('cloudinary').v2;
    
    const transformationString = transformations || 'c_fill,w_800,h_600,q_auto,f_auto';
    
    const transformedUrl = cloudinary.url(publicId, {
      transformation: transformationString
    });

    res.status(200).json({
      success: true,
      data: {
        originalPublicId: publicId,
        transformedUrl,
        transformations: transformationString
      },
      message: 'Image processed successfully'
    });
  } catch (error) {
    console.error('Process image error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process image'
    });
  }
};

// @desc    Get image metadata
// @route   GET /api/upload/metadata/:publicId
// @access  Private
const getImageMetadata = async (req, res) => {
  try {
    const { publicId } = req.params;

    if (!publicId) {
      return res.status(400).json({
        success: false,
        message: 'Public ID is required'
      });
    }

    // Get image details from Cloudinary
    const cloudinary = require('cloudinary').v2;
    
    const result = await cloudinary.api.resource(publicId, {
      image_metadata: true,
      colors: true,
      faces: true
    });

    res.status(200).json({
      success: true,
      data: {
        publicId: result.public_id,
        format: result.format,
        width: result.width,
        height: result.height,
        bytes: result.bytes,
        url: result.secure_url,
        createdAt: result.created_at,
        metadata: result.image_metadata,
        colors: result.colors,
        faces: result.faces
      }
    });
  } catch (error) {
    console.error('Get image metadata error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get image metadata'
    });
  }
};

module.exports = {
  uploadSingleImage,
  uploadMultipleImagesController,
  deleteImageController,
  getUploadConfig,
  generateSignedUploadUrl,
  processImage,
  getImageMetadata
};
