const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// Image optimization configurations
const OPTIMIZATION_CONFIGS = {
  thumbnail: {
    width: 300,
    height: 200,
    quality: 80,
    format: 'webp'
  },
  medium: {
    width: 800,
    height: 600,
    quality: 85,
    format: 'webp'
  },
  large: {
    width: 1200,
    height: 800,
    quality: 90,
    format: 'webp'
  },
  profile: {
    width: 400,
    height: 400,
    quality: 85,
    format: 'webp',
    crop: 'cover'
  }
};

// Optimize single image
const optimizeImage = async (inputPath, outputPath, config = 'medium') => {
  try {
    const optimization = OPTIMIZATION_CONFIGS[config] || OPTIMIZATION_CONFIGS.medium;
    
    let sharpInstance = sharp(inputPath);

    // Resize image
    if (optimization.crop === 'cover') {
      sharpInstance = sharpInstance.resize(optimization.width, optimization.height, {
        fit: 'cover',
        position: 'center'
      });
    } else {
      sharpInstance = sharpInstance.resize(optimization.width, optimization.height, {
        fit: 'inside',
        withoutEnlargement: true
      });
    }

    // Apply format and quality
    switch (optimization.format) {
      case 'webp':
        sharpInstance = sharpInstance.webp({ quality: optimization.quality });
        break;
      case 'jpeg':
        sharpInstance = sharpInstance.jpeg({ quality: optimization.quality });
        break;
      case 'png':
        sharpInstance = sharpInstance.png({ quality: optimization.quality });
        break;
      default:
        sharpInstance = sharpInstance.webp({ quality: optimization.quality });
    }

    // Save optimized image
    await sharpInstance.toFile(outputPath);

    // Get file stats
    const stats = fs.statSync(outputPath);
    const metadata = await sharp(outputPath).metadata();

    return {
      success: true,
      outputPath,
      size: stats.size,
      width: metadata.width,
      height: metadata.height,
      format: metadata.format
    };
  } catch (error) {
    console.error('Image optimization error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Create multiple optimized versions of an image
const createImageVariants = async (inputPath, outputDir, filename) => {
  try {
    const variants = {};
    const baseFilename = path.parse(filename).name;

    // Create output directory if it doesn't exist
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Generate different variants
    for (const [variantName, config] of Object.entries(OPTIMIZATION_CONFIGS)) {
      const outputPath = path.join(outputDir, `${baseFilename}_${variantName}.${config.format}`);
      
      const result = await optimizeImage(inputPath, outputPath, variantName);
      
      if (result.success) {
        variants[variantName] = {
          path: outputPath,
          size: result.size,
          width: result.width,
          height: result.height,
          format: result.format
        };
      }
    }

    return {
      success: true,
      variants
    };
  } catch (error) {
    console.error('Create image variants error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Compress image without resizing
const compressImage = async (inputPath, outputPath, quality = 85) => {
  try {
    const metadata = await sharp(inputPath).metadata();
    
    let sharpInstance = sharp(inputPath);

    // Apply compression based on original format
    switch (metadata.format) {
      case 'jpeg':
        sharpInstance = sharpInstance.jpeg({ quality });
        break;
      case 'png':
        sharpInstance = sharpInstance.png({ quality });
        break;
      case 'webp':
        sharpInstance = sharpInstance.webp({ quality });
        break;
      default:
        // Convert to WebP for better compression
        sharpInstance = sharpInstance.webp({ quality });
    }

    await sharpInstance.toFile(outputPath);

    const stats = fs.statSync(outputPath);
    const newMetadata = await sharp(outputPath).metadata();

    return {
      success: true,
      outputPath,
      originalSize: fs.statSync(inputPath).size,
      compressedSize: stats.size,
      compressionRatio: ((fs.statSync(inputPath).size - stats.size) / fs.statSync(inputPath).size * 100).toFixed(2),
      width: newMetadata.width,
      height: newMetadata.height,
      format: newMetadata.format
    };
  } catch (error) {
    console.error('Image compression error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Generate image thumbnail
const generateThumbnail = async (inputPath, outputPath, size = 300) => {
  try {
    await sharp(inputPath)
      .resize(size, size, {
        fit: 'cover',
        position: 'center'
      })
      .webp({ quality: 80 })
      .toFile(outputPath);

    const stats = fs.statSync(outputPath);
    const metadata = await sharp(outputPath).metadata();

    return {
      success: true,
      outputPath,
      size: stats.size,
      width: metadata.width,
      height: metadata.height
    };
  } catch (error) {
    console.error('Thumbnail generation error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Add watermark to image
const addWatermark = async (inputPath, outputPath, watermarkPath, options = {}) => {
  try {
    const {
      position = 'southeast',
      opacity = 0.5,
      margin = 20
    } = options;

    // Load and prepare watermark
    const watermark = await sharp(watermarkPath)
      .resize(200, null, { withoutEnlargement: true })
      .png()
      .toBuffer();

    // Apply watermark
    await sharp(inputPath)
      .composite([{
        input: watermark,
        gravity: position,
        blend: 'over'
      }])
      .toFile(outputPath);

    return {
      success: true,
      outputPath
    };
  } catch (error) {
    console.error('Watermark error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Get image information
const getImageInfo = async (imagePath) => {
  try {
    const metadata = await sharp(imagePath).metadata();
    const stats = fs.statSync(imagePath);

    return {
      success: true,
      info: {
        width: metadata.width,
        height: metadata.height,
        format: metadata.format,
        size: stats.size,
        density: metadata.density,
        hasAlpha: metadata.hasAlpha,
        channels: metadata.channels,
        colorspace: metadata.space
      }
    };
  } catch (error) {
    console.error('Get image info error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Validate image file
const validateImage = async (imagePath, options = {}) => {
  try {
    const {
      maxWidth = 5000,
      maxHeight = 5000,
      maxSize = 10 * 1024 * 1024, // 10MB
      allowedFormats = ['jpeg', 'jpg', 'png', 'webp', 'gif']
    } = options;

    const info = await getImageInfo(imagePath);
    
    if (!info.success) {
      return {
        isValid: false,
        errors: ['Invalid image file']
      };
    }

    const errors = [];

    // Check format
    if (!allowedFormats.includes(info.info.format.toLowerCase())) {
      errors.push(`Format ${info.info.format} is not allowed`);
    }

    // Check dimensions
    if (info.info.width > maxWidth) {
      errors.push(`Image width ${info.info.width}px exceeds maximum ${maxWidth}px`);
    }

    if (info.info.height > maxHeight) {
      errors.push(`Image height ${info.info.height}px exceeds maximum ${maxHeight}px`);
    }

    // Check file size
    if (info.info.size > maxSize) {
      errors.push(`File size ${(info.info.size / 1024 / 1024).toFixed(2)}MB exceeds maximum ${(maxSize / 1024 / 1024).toFixed(2)}MB`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      info: info.info
    };
  } catch (error) {
    console.error('Image validation error:', error);
    return {
      isValid: false,
      errors: ['Failed to validate image']
    };
  }
};

module.exports = {
  optimizeImage,
  createImageVariants,
  compressImage,
  generateThumbnail,
  addWatermark,
  getImageInfo,
  validateImage,
  OPTIMIZATION_CONFIGS
};
