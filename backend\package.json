{"name": "wanderlust-backend", "version": "1.0.0", "description": "Backend API for Wanderlust travel booking platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["travel", "booking", "tours", "api"], "author": "Wanderlust Team", "license": "MIT", "dependencies": {"bcryptjs": "^3.0.2", "cloudinary": "^2.7.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.18.2", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.4", "morgan": "^1.10.1", "multer": "^2.0.2", "nodemon": "^3.1.10", "sharp": "^0.34.3"}}