import React from 'react';
import { Link } from 'react-router-dom';
import { MapPin, Star, Clock, Users, Heart, Calendar } from 'lucide-react';
import Card from '../ui/Card';
import Badge from '../ui/Badge';
import Button from '../ui/Button';

const TourCard = ({ 
  tour, 
  showWishlist = true, 
  onWishlistToggle,
  isWishlisted = false,
  className = '' 
}) => {
  const {
    _id,
    name,
    slug,
    destination,
    price,
    originalPrice,
    currency = 'USD',
    ratingsAverage,
    ratingsQuantity,
    duration,
    maxGroupSize,
    category,
    images,
    isFeatured,
    nextAvailableDate
  } = tour;

  const mainImage = images?.find(img => img.isMain) || images?.[0];
  const discountPercentage = originalPrice && originalPrice > price 
    ? Math.round(((originalPrice - price) / originalPrice) * 100)
    : 0;

  const formatPrice = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <Card hover className={`overflow-hidden group ${className}`}>
      {/* Image Section */}
      <div className="relative overflow-hidden">
        <img
          src={mainImage?.url || '/placeholder-tour.jpg'}
          alt={name}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        
        {/* Overlay Badges */}
        <div className="absolute top-3 left-3 flex flex-col gap-2">
          {isFeatured && (
            <Badge variant="warning" className="shadow-sm">
              Featured
            </Badge>
          )}
          {discountPercentage > 0 && (
            <Badge variant="danger" className="shadow-sm">
              {discountPercentage}% OFF
            </Badge>
          )}
        </div>

        <div className="absolute top-3 right-3 flex flex-col gap-2">
          <Badge variant="primary" className="shadow-sm">
            {category}
          </Badge>
          {showWishlist && (
            <button
              onClick={(e) => {
                e.preventDefault();
                onWishlistToggle?.(tour);
              }}
              className={`p-2 rounded-full shadow-sm transition-all duration-300 transform hover:scale-110 ${
                isWishlisted
                  ? 'bg-red-500 text-white shadow-red-200'
                  : 'bg-white text-neutral-600 hover:text-red-500 hover:bg-red-50'
              }`}
            >
              <Heart className={`w-4 h-4 transition-all duration-300 ${
                isWishlisted ? 'fill-current text-white' : 'hover:text-red-500'
              }`} />
            </button>
          )}
        </div>

        {/* Quick Info Overlay */}
        <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="bg-black bg-opacity-75 text-white p-2 rounded-lg text-xs">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center">
                  <Clock className="w-3 h-3 mr-1" />
                  {duration.days}d/{duration.nights}n
                </div>
                <div className="flex items-center">
                  <Users className="w-3 h-3 mr-1" />
                  Max {maxGroupSize}
                </div>
              </div>
              {nextAvailableDate && (
                <div className="flex items-center">
                  <Calendar className="w-3 h-3 mr-1" />
                  {formatDate(nextAvailableDate.startDate)}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <Card.Content className="p-4">
        {/* Location */}
        <div className="flex items-center text-sm text-neutral-500 mb-2">
          <MapPin className="w-4 h-4 mr-1 flex-shrink-0" />
          <span className="truncate">
            {destination.city}, {destination.country}
          </span>
        </div>

        {/* Title */}
        <h3 className="text-lg font-semibold text-neutral-900 mb-2 line-clamp-2">
          {name}
        </h3>

        {/* Rating */}
        <div className="flex items-center mb-3">
          <div className="flex items-center">
            <Star className="w-4 h-4 text-yellow-400 fill-current" />
            <span className="ml-1 text-sm font-medium">
              {ratingsAverage ? ratingsAverage.toFixed(1) : 'New'}
            </span>
          </div>
          {ratingsQuantity > 0 && (
            <span className="text-sm text-neutral-500 ml-2">
              ({ratingsQuantity} reviews)
            </span>
          )}
        </div>

        {/* Price and Action */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-2xl font-bold text-primary-600">
              {formatPrice(price)}
            </span>
            {originalPrice && originalPrice > price && (
              <span className="text-sm text-neutral-500 line-through">
                {formatPrice(originalPrice)}
              </span>
            )}
          </div>
          
          <Link to={`/tours/${slug || _id}`}>
            <Button variant="primary" size="sm">
              View Details
            </Button>
          </Link>
        </div>

        {/* Availability Status */}
        {nextAvailableDate && (
          <div className="mt-3 pt-3 border-t border-neutral-200">
            <div className="flex items-center justify-between text-xs">
              <span className="text-neutral-500">Next available:</span>
              <span className="font-medium text-accent-600">
                {formatDate(nextAvailableDate.startDate)}
              </span>
            </div>
          </div>
        )}
      </Card.Content>
    </Card>
  );
};

export default TourCard;
