import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  MapPin, Star, Clock, Users, Calendar, Heart, Share2,
  CheckCircle, XCircle, AlertCircle, Camera, ArrowLeft,
  Shield, Award, Globe, Wifi
} from 'lucide-react';
import { tourAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import Badge from '../components/ui/Badge';
import LoadingSpinner, { PageLoader } from '../components/ui/LoadingSpinner';
import TourCard from '../components/tours/TourCard';
import toast from 'react-hot-toast';

const TourDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const [tour, setTour] = useState(null);
  const [similarTours, setSimilarTours] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [activeTab, setActiveTab] = useState('overview');
  const [isWishlisted, setIsWishlisted] = useState(false);

  useEffect(() => {
    fetchTourDetails();
  }, [id]);

  const fetchTourDetails = async () => {
    try {
      setLoading(true);
      const [tourResponse, similarResponse] = await Promise.all([
        tourAPI.getTour(id),
        tourAPI.getSimilarTours(id, { limit: 4 })
      ]);

      setTour(tourResponse.data.data);
      setSimilarTours(similarResponse.data.data);
    } catch (error) {
      console.error('Error fetching tour details:', error);
      toast.error('Failed to load tour details');
      navigate('/tours');
    } finally {
      setLoading(false);
    }
  };

  const handleBookNow = () => {
    if (!isAuthenticated) {
      toast.error('Please login to book a tour');
      navigate('/login', { state: { from: { pathname: `/book/${tour._id}` } } });
      return;
    }
    navigate(`/book/${tour._id}`);
  };

  const handleWishlistToggle = () => {
    if (!isAuthenticated) {
      toast.error('Please login to add to wishlist');
      navigate('/login');
      return;
    }
    setIsWishlisted(!isWishlisted);
    toast.success(isWishlisted ? 'Removed from wishlist' : 'Added to wishlist');
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: tour.name,
          text: tour.shortDescription,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard!');
    }
  };

  const formatPrice = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: tour?.currency || 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'itinerary', label: 'Itinerary' },
    { id: 'included', label: 'What\'s Included' },
    { id: 'reviews', label: 'Reviews' },
  ];

  if (loading) {
    return <PageLoader message="Loading tour details..." />;
  }

  if (!tour) {
    return (
      <div className="container-custom py-12 text-center">
        <h1 className="text-2xl font-bold text-neutral-900 mb-4">Tour not found</h1>
        <Button onClick={() => navigate('/tours')}>
          Back to Tours
        </Button>
      </div>
    );
  }

  const mainImage = tour.images?.find(img => img.isMain) || tour.images?.[0];
  const discountPercentage = tour.originalPrice && tour.originalPrice > tour.price
    ? Math.round(((tour.originalPrice - tour.price) / tour.originalPrice) * 100)
    : 0;

  return (
    <div className="min-h-screen bg-white">
      {/* Back Button */}
      <div className="container-custom pt-6">
        <Button
          variant="ghost"
          icon={ArrowLeft}
          onClick={() => navigate(-1)}
          className="mb-4"
        >
          Back
        </Button>
      </div>

      {/* Image Gallery */}
      <div className="container-custom mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 h-96 lg:h-[500px]">
          {/* Main Image */}
          <div className="lg:col-span-2 relative overflow-hidden rounded-xl">
            <img
              src={tour.images?.[selectedImageIndex]?.url || mainImage?.url}
              alt={tour.name}
              className="w-full h-full object-cover"
            />
            <div className="absolute top-4 left-4 flex gap-2">
              {tour.isFeatured && (
                <Badge variant="warning">Featured</Badge>
              )}
              {discountPercentage > 0 && (
                <Badge variant="danger">{discountPercentage}% OFF</Badge>
              )}
            </div>
            <div className="absolute top-4 right-4 flex gap-2">
              <button
                onClick={handleWishlistToggle}
                className={`p-2 rounded-full shadow-lg transition-colors ${
                  isWishlisted
                    ? 'bg-red-500 text-white'
                    : 'bg-white text-neutral-600 hover:text-red-500'
                }`}
              >
                <Heart className={`w-5 h-5 ${isWishlisted ? 'fill-current' : ''}`} />
              </button>
              <button
                onClick={handleShare}
                className="p-2 bg-white text-neutral-600 hover:text-primary-600 rounded-full shadow-lg transition-colors"
              >
                <Share2 className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Thumbnail Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-1 gap-4">
            {tour.images?.slice(0, 4).map((image, index) => (
              <div
                key={index}
                className={`relative overflow-hidden rounded-lg cursor-pointer transition-all ${
                  selectedImageIndex === index
                    ? 'ring-2 ring-primary-500'
                    : 'hover:opacity-80'
                }`}
                onClick={() => setSelectedImageIndex(index)}
              >
                <img
                  src={image.url}
                  alt={`${tour.name} ${index + 1}`}
                  className="w-full h-full object-cover"
                />
                {index === 3 && tour.images.length > 4 && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <div className="text-white text-center">
                      <Camera className="w-6 h-6 mx-auto mb-1" />
                      <span className="text-sm">+{tour.images.length - 4} more</span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="container-custom">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Header */}
            <div className="mb-6">
              <div className="flex items-center gap-2 text-sm text-neutral-500 mb-2">
                <MapPin className="w-4 h-4" />
                {tour.destination.city}, {tour.destination.country}
                <Badge variant="primary" className="ml-2">
                  {tour.category}
                </Badge>
              </div>

              <h1 className="text-3xl lg:text-4xl font-bold text-neutral-900 mb-4">
                {tour.name}
              </h1>

              <div className="flex items-center gap-6 mb-4">
                <div className="flex items-center">
                  <Star className="w-5 h-5 text-yellow-400 fill-current mr-1" />
                  <span className="font-medium">
                    {tour.ratingsAverage ? tour.ratingsAverage.toFixed(1) : 'New'}
                  </span>
                  {tour.ratingsQuantity > 0 && (
                    <span className="text-neutral-500 ml-1">
                      ({tour.ratingsQuantity} reviews)
                    </span>
                  )}
                </div>

                <div className="flex items-center text-neutral-600">
                  <Clock className="w-4 h-4 mr-1" />
                  {tour.duration.days} days, {tour.duration.nights} nights
                </div>

                <div className="flex items-center text-neutral-600">
                  <Users className="w-4 h-4 mr-1" />
                  Max {tour.maxGroupSize} people
                </div>
              </div>

              <p className="text-lg text-neutral-600">
                {tour.shortDescription}
              </p>
            </div>

            {/* Tabs */}
            <div className="border-b border-neutral-200 mb-6">
              <nav className="flex space-x-8">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-neutral-500 hover:text-neutral-700'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="mb-8">
              {activeTab === 'overview' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-semibold mb-3">About This Tour</h3>
                    <p className="text-neutral-600 leading-relaxed">
                      {tour.description}
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold mb-3">Tour Highlights</h4>
                      <ul className="space-y-2">
                        {tour.included?.slice(0, 5).map((item, index) => (
                          <li key={index} className="flex items-start">
                            <CheckCircle className="w-4 h-4 text-accent-500 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-neutral-600">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-3">Important Info</h4>
                      <div className="space-y-3">
                        <div className="flex items-center">
                          <Shield className="w-4 h-4 text-primary-500 mr-2" />
                          <span className="text-sm text-neutral-600">
                            Difficulty: {tour.difficulty}
                          </span>
                        </div>
                        <div className="flex items-center">
                          <Globe className="w-4 h-4 text-primary-500 mr-2" />
                          <span className="text-sm text-neutral-600">
                            Language: English
                          </span>
                        </div>
                        <div className="flex items-center">
                          <Award className="w-4 h-4 text-primary-500 mr-2" />
                          <span className="text-sm text-neutral-600">
                            Licensed Guide Included
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'itinerary' && (
                <div>
                  <h3 className="text-xl font-semibold mb-6">Day by Day Itinerary</h3>
                  <div className="space-y-6">
                    {tour.itinerary?.map((day, index) => (
                      <Card key={index} className="p-6">
                        <div className="flex items-start">
                          <div className="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                            <span className="text-primary-600 font-semibold">
                              {day.day}
                            </span>
                          </div>
                          <div className="flex-1">
                            <h4 className="text-lg font-semibold mb-2">{day.title}</h4>
                            <p className="text-neutral-600 mb-4">{day.description}</p>

                            {day.activities && day.activities.length > 0 && (
                              <div className="mb-4">
                                <h5 className="font-medium mb-2">Activities:</h5>
                                <ul className="list-disc list-inside text-sm text-neutral-600 space-y-1">
                                  {day.activities.map((activity, actIndex) => (
                                    <li key={actIndex}>{activity}</li>
                                  ))}
                                </ul>
                              </div>
                            )}

                            <div className="flex items-center gap-4 text-sm text-neutral-500">
                              {day.meals && (
                                <div className="flex items-center gap-2">
                                  <span>Meals:</span>
                                  {day.meals.breakfast && <span className="bg-neutral-100 px-2 py-1 rounded">Breakfast</span>}
                                  {day.meals.lunch && <span className="bg-neutral-100 px-2 py-1 rounded">Lunch</span>}
                                  {day.meals.dinner && <span className="bg-neutral-100 px-2 py-1 rounded">Dinner</span>}
                                </div>
                              )}
                              {day.accommodation && (
                                <div>
                                  <span>Stay: {day.accommodation}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'included' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-xl font-semibold mb-4 text-accent-600">
                      What's Included
                    </h3>
                    <ul className="space-y-3">
                      {tour.included?.map((item, index) => (
                        <li key={index} className="flex items-start">
                          <CheckCircle className="w-5 h-5 text-accent-500 mr-3 mt-0.5 flex-shrink-0" />
                          <span className="text-neutral-600">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold mb-4 text-red-600">
                      What's Not Included
                    </h3>
                    <ul className="space-y-3">
                      {tour.excluded?.map((item, index) => (
                        <li key={index} className="flex items-start">
                          <XCircle className="w-5 h-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" />
                          <span className="text-neutral-600">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {activeTab === 'reviews' && (
                <div>
                  <h3 className="text-xl font-semibold mb-6">Customer Reviews</h3>
                  <div className="text-center py-12 text-neutral-500">
                    <Star className="w-12 h-12 mx-auto mb-4 text-neutral-300" />
                    <p>Reviews coming soon...</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Booking Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-6">
              <Card className="p-6">
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-3xl font-bold text-primary-600">
                        {formatPrice(tour.price)}
                      </span>
                      {tour.originalPrice && tour.originalPrice > tour.price && (
                        <span className="text-lg text-neutral-500 line-through">
                          {formatPrice(tour.originalPrice)}
                        </span>
                      )}
                    </div>
                    {discountPercentage > 0 && (
                      <Badge variant="danger">
                        Save {discountPercentage}%
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-neutral-500">per person</p>
                </div>

                {/* Next Available Date */}
                {tour.nextAvailableDate && (
                  <div className="mb-6 p-4 bg-accent-50 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Calendar className="w-4 h-4 text-accent-600 mr-2" />
                      <span className="font-medium text-accent-800">Next Available</span>
                    </div>
                    <p className="text-accent-700">
                      {new Date(tour.nextAvailableDate.startDate).toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </p>
                    <p className="text-sm text-accent-600 mt-1">
                      {tour.nextAvailableDate.availableSpots} spots remaining
                    </p>
                  </div>
                )}

                <Button
                  variant="primary"
                  size="lg"
                  fullWidth
                  onClick={handleBookNow}
                  className="mb-4"
                >
                  Book Now
                </Button>

                <div className="text-center text-sm text-neutral-500 mb-6">
                  Free cancellation up to 24 hours before
                </div>

                {/* Tour Provider */}
                {tour.provider && (
                  <div className="border-t pt-6">
                    <h4 className="font-semibold mb-3">Tour Provider</h4>
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-neutral-200 rounded-full flex items-center justify-center mr-3">
                        {tour.provider.profileImage?.url ? (
                          <img
                            src={tour.provider.profileImage.url}
                            alt={tour.provider.fullName}
                            className="w-12 h-12 rounded-full object-cover"
                          />
                        ) : (
                          <span className="text-neutral-600 font-medium">
                            {tour.provider.firstName?.[0]}{tour.provider.lastName?.[0]}
                          </span>
                        )}
                      </div>
                      <div>
                        <p className="font-medium">
                          {tour.provider.companyInfo?.companyName ||
                           `${tour.provider.firstName} ${tour.provider.lastName}`}
                        </p>
                        <p className="text-sm text-neutral-500">Verified Provider</p>
                      </div>
                    </div>
                  </div>
                )}
              </Card>
            </div>
          </div>
        </div>

        {/* Similar Tours */}
        {similarTours.length > 0 && (
          <div className="mt-16">
            <h2 className="text-2xl font-bold text-neutral-900 mb-8">
              Similar Tours You Might Like
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {similarTours.map((similarTour) => (
                <TourCard key={similarTour._id} tour={similarTour} />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TourDetailPage;
