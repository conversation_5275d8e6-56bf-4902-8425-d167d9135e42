import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  MapPin, Star, Clock, Users, Calendar, Heart, Share2,
  CheckCircle, XCircle, AlertCircle, ArrowLeft,
  Shield, Award, Globe, Wifi, MessageCircle, ThumbsUp
} from 'lucide-react';
import { tourAPI, reviewAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import Badge from '../components/ui/Badge';
import LoadingSpinner, { PageLoader } from '../components/ui/LoadingSpinner';
import TourCard from '../components/tours/TourCard';
import toast from 'react-hot-toast';

const TourDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const [tour, setTour] = useState(null);
  const [similarTours, setSimilarTours] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [reviews, setReviews] = useState([]);
  const [reviewsLoading, setReviewsLoading] = useState(false);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [newReview, setNewReview] = useState({
    rating: 5,
    review: '',
    detailedRatings: {
      valueForMoney: 5,
      service: 5,
      organization: 5,
      safety: 5
    }
  });

  useEffect(() => {
    fetchTourDetails();
  }, [id]);

  useEffect(() => {
    if (tour && activeTab === 'reviews') {
      fetchReviews();
    }
  }, [tour, activeTab]);

  const fetchTourDetails = async () => {
    try {
      setLoading(true);
      const [tourResponse, similarResponse] = await Promise.all([
        tourAPI.getTour(id),
        tourAPI.getSimilarTours(id, { limit: 4 })
      ]);

      setTour(tourResponse.data.data);
      setSimilarTours(similarResponse.data.data);
    } catch (error) {
      console.error('Error fetching tour details:', error);
      toast.error('Failed to load tour details');
      navigate('/tours');
    } finally {
      setLoading(false);
    }
  };

  const fetchReviews = async () => {
    try {
      setReviewsLoading(true);
      const response = await tourAPI.getTourReviews(id);
      setReviews(response.data.data);
    } catch (error) {
      console.error('Error fetching reviews:', error);
      toast.error('Failed to load reviews');
    } finally {
      setReviewsLoading(false);
    }
  };

  const handleSubmitReview = async () => {
    try {
      if (newReview.review.trim().length < 10) {
        toast.error('Review must be at least 10 characters long');
        return;
      }

      const response = await tourAPI.createReview(id, newReview);
      setReviews(prev => [response.data.data, ...prev]);
      toast.success('Review submitted successfully!');
      setShowReviewForm(false);
      setNewReview({
        rating: 5,
        review: '',
        detailedRatings: {
          valueForMoney: 5,
          service: 5,
          organization: 5,
          safety: 5
        }
      });
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error(error.response?.data?.message || 'Failed to submit review');
    }
  };

  const handleMarkHelpful = async (reviewId) => {
    try {
      await reviewAPI.markHelpful(reviewId);
      setReviews(prev => prev.map(review =>
        review._id === reviewId
          ? { ...review, helpfulVotes: review.helpfulVotes + 1 }
          : review
      ));
      toast.success('Marked as helpful!');
    } catch (error) {
      console.error('Error marking review as helpful:', error);
      toast.error('Failed to mark as helpful');
    }
  };

  const handleBookNow = () => {
    if (!isAuthenticated) {
      toast.error('Please login to book a tour');
      navigate('/login', { state: { from: { pathname: `/book/${tour._id}` } } });
      return;
    }
    navigate(`/book/${tour._id}`);
  };

  const handleWishlistToggle = () => {
    if (!isAuthenticated) {
      toast.error('Please login to add to wishlist');
      navigate('/login');
      return;
    }
    setIsWishlisted(!isWishlisted);
    toast.success(isWishlisted ? 'Removed from wishlist' : 'Added to wishlist');
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: tour.name,
          text: tour.shortDescription,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard!');
    }
  };

  const formatPrice = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: tour?.currency || 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'itinerary', label: 'Itinerary' },
    { id: 'included', label: 'What\'s Included' },
    { id: 'reviews', label: 'Reviews' },
  ];

  if (loading) {
    return <PageLoader message="Loading tour details..." />;
  }

  if (!tour) {
    return (
      <div className="container-custom py-12 text-center">
        <h1 className="text-2xl font-bold text-neutral-900 mb-4">Tour not found</h1>
        <Button onClick={() => navigate('/tours')}>
          Back to Tours
        </Button>
      </div>
    );
  }

  const mainImage = tour.images?.find(img => img.isMain) || tour.images?.[0];
  const discountPercentage = tour.originalPrice && tour.originalPrice > tour.price
    ? Math.round(((tour.originalPrice - tour.price) / tour.originalPrice) * 100)
    : 0;

  return (
    <div className="min-h-screen bg-white">
      {/* Back Button */}
      <div className="container-custom pt-6">
        <Button
          variant="ghost"
          icon={ArrowLeft}
          onClick={() => navigate(-1)}
          className="mb-4"
        >
          Back
        </Button>
      </div>

      {/* Hero Image */}
      <div className="container-custom mb-8">
        <div className="relative overflow-hidden rounded-xl h-96 lg:h-[500px]">
          <img
            src={mainImage?.url}
            alt={tour.name}
            className="w-full h-full object-cover"
          />
          <div className="absolute top-4 left-4 flex gap-2">
            {tour.isFeatured && (
              <Badge variant="warning">Featured</Badge>
            )}
            {discountPercentage > 0 && (
              <Badge variant="danger">{discountPercentage}% OFF</Badge>
            )}
          </div>
          <div className="absolute top-4 right-4 flex gap-2">
            <button
              onClick={handleWishlistToggle}
              className={`p-3 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110 ${
                isWishlisted
                  ? 'bg-red-500 text-white shadow-red-200'
                  : 'bg-white text-neutral-600 hover:text-red-500 hover:bg-red-50'
              }`}
            >
              <Heart className={`w-6 h-6 transition-all duration-300 ${
                isWishlisted ? 'fill-current text-white' : 'hover:text-red-500'
              }`} />
            </button>
            <button
              onClick={handleShare}
              className="p-3 bg-white text-neutral-600 hover:text-primary-600 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110"
            >
              <Share2 className="w-6 h-6" />
            </button>
          </div>
        </div>
      </div>

      <div className="container-custom">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Header */}
            <div className="mb-6">
              <div className="flex items-center gap-2 text-sm text-neutral-500 mb-2">
                <MapPin className="w-4 h-4" />
                {tour.destination.city}, {tour.destination.country}
                <Badge variant="primary" className="ml-2">
                  {tour.category}
                </Badge>
              </div>

              <h1 className="text-3xl lg:text-4xl font-bold text-neutral-900 mb-4">
                {tour.name}
              </h1>

              <div className="flex items-center gap-6 mb-4">
                <div className="flex items-center">
                  <Star className="w-5 h-5 text-yellow-400 fill-current mr-1" />
                  <span className="font-medium">
                    {tour.ratingsAverage ? tour.ratingsAverage.toFixed(1) : 'New'}
                  </span>
                  {tour.ratingsQuantity > 0 && (
                    <span className="text-neutral-500 ml-1">
                      ({tour.ratingsQuantity} reviews)
                    </span>
                  )}
                </div>

                <div className="flex items-center text-neutral-600">
                  <Clock className="w-4 h-4 mr-1" />
                  {tour.duration.days} days, {tour.duration.nights} nights
                </div>

                <div className="flex items-center text-neutral-600">
                  <Users className="w-4 h-4 mr-1" />
                  Max {tour.maxGroupSize} people
                </div>
              </div>

              <p className="text-lg text-neutral-600">
                {tour.shortDescription}
              </p>
            </div>

            {/* Tabs */}
            <div className="border-b border-neutral-200 mb-6">
              <nav className="flex space-x-8">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-neutral-500 hover:text-neutral-700'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="mb-8">
              {activeTab === 'overview' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-semibold mb-3">About This Tour</h3>
                    <p className="text-neutral-600 leading-relaxed">
                      {tour.description}
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold mb-3">Tour Highlights</h4>
                      <ul className="space-y-2">
                        {tour.included?.slice(0, 5).map((item, index) => (
                          <li key={index} className="flex items-start">
                            <CheckCircle className="w-4 h-4 text-accent-500 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-neutral-600">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-3">Important Info</h4>
                      <div className="space-y-3">
                        <div className="flex items-center">
                          <Shield className="w-4 h-4 text-primary-500 mr-2" />
                          <span className="text-sm text-neutral-600">
                            Difficulty: {tour.difficulty}
                          </span>
                        </div>
                        <div className="flex items-center">
                          <Globe className="w-4 h-4 text-primary-500 mr-2" />
                          <span className="text-sm text-neutral-600">
                            Language: English
                          </span>
                        </div>
                        <div className="flex items-center">
                          <Award className="w-4 h-4 text-primary-500 mr-2" />
                          <span className="text-sm text-neutral-600">
                            Licensed Guide Included
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'itinerary' && (
                <div>
                  <h3 className="text-xl font-semibold mb-6">Day by Day Itinerary</h3>
                  <div className="space-y-6">
                    {tour.itinerary?.map((day, index) => (
                      <Card key={index} className="p-6">
                        <div className="flex items-start">
                          <div className="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                            <span className="text-primary-600 font-semibold">
                              {day.day}
                            </span>
                          </div>
                          <div className="flex-1">
                            <h4 className="text-lg font-semibold mb-2">{day.title}</h4>
                            <p className="text-neutral-600 mb-4">{day.description}</p>

                            {day.activities && day.activities.length > 0 && (
                              <div className="mb-4">
                                <h5 className="font-medium mb-2">Activities:</h5>
                                <ul className="list-disc list-inside text-sm text-neutral-600 space-y-1">
                                  {day.activities.map((activity, actIndex) => (
                                    <li key={actIndex}>{activity}</li>
                                  ))}
                                </ul>
                              </div>
                            )}

                            <div className="flex items-center gap-4 text-sm text-neutral-500">
                              {day.meals && (
                                <div className="flex items-center gap-2">
                                  <span>Meals:</span>
                                  {day.meals.breakfast && <span className="bg-neutral-100 px-2 py-1 rounded">Breakfast</span>}
                                  {day.meals.lunch && <span className="bg-neutral-100 px-2 py-1 rounded">Lunch</span>}
                                  {day.meals.dinner && <span className="bg-neutral-100 px-2 py-1 rounded">Dinner</span>}
                                </div>
                              )}
                              {day.accommodation && (
                                <div>
                                  <span>Stay: {day.accommodation}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'included' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-xl font-semibold mb-4 text-accent-600">
                      What's Included
                    </h3>
                    <ul className="space-y-3">
                      {tour.included?.map((item, index) => (
                        <li key={index} className="flex items-start">
                          <CheckCircle className="w-5 h-5 text-accent-500 mr-3 mt-0.5 flex-shrink-0" />
                          <span className="text-neutral-600">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold mb-4 text-red-600">
                      What's Not Included
                    </h3>
                    <ul className="space-y-3">
                      {tour.excluded?.map((item, index) => (
                        <li key={index} className="flex items-start">
                          <XCircle className="w-5 h-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" />
                          <span className="text-neutral-600">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {activeTab === 'reviews' && (
                <div>
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-semibold">Customer Reviews</h3>
                    {isAuthenticated ? (
                      <Button
                        variant="outline"
                        icon={MessageCircle}
                        onClick={() => setShowReviewForm(!showReviewForm)}
                      >
                        Write Review
                      </Button>
                    ) : (
                      <Button
                        variant="outline"
                        icon={MessageCircle}
                        onClick={() => {
                          toast.error('Please login to write a review');
                          navigate('/login');
                        }}
                      >
                        Login to Review
                      </Button>
                    )}
                  </div>

                  {/* Review Form */}
                  {showReviewForm && isAuthenticated && (
                    <Card className="p-6 mb-6 border-2 border-primary-200">
                      <h4 className="text-lg font-semibold mb-4">Write Your Review</h4>
                      <div className="space-y-4">
                        {/* Overall Rating */}
                        <div>
                          <label className="block text-sm font-medium text-neutral-700 mb-2">
                            Overall Rating
                          </label>
                          <div className="flex items-center gap-1">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <button
                                key={star}
                                onClick={() => setNewReview(prev => ({ ...prev, rating: star }))}
                                className="p-1"
                              >
                                <Star
                                  className={`w-6 h-6 ${
                                    star <= newReview.rating
                                      ? 'text-yellow-400 fill-current'
                                      : 'text-neutral-300'
                                  }`}
                                />
                              </button>
                            ))}
                            <span className="ml-2 text-sm text-neutral-600">
                              {newReview.rating} star{newReview.rating !== 1 ? 's' : ''}
                            </span>
                          </div>
                        </div>

                        {/* Review Text */}
                        <div>
                          <label className="block text-sm font-medium text-neutral-700 mb-2">
                            Your Review
                          </label>
                          <textarea
                            value={newReview.review}
                            onChange={(e) => setNewReview(prev => ({ ...prev, review: e.target.value }))}
                            placeholder="Share your experience with this tour..."
                            className="w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                            rows={4}
                            maxLength={1000}
                          />
                          <div className="text-right text-xs text-neutral-500 mt-1">
                            {newReview.review.length}/1000 characters
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-3 pt-2">
                          <Button
                            variant="primary"
                            onClick={handleSubmitReview}
                          >
                            Submit Review
                          </Button>
                          <Button
                            variant="ghost"
                            onClick={() => setShowReviewForm(false)}
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    </Card>
                  )}

                  {/* Reviews List */}
                  <div className="space-y-6">
                    {reviewsLoading ? (
                      <div className="text-center py-8">
                        <LoadingSpinner />
                        <p className="text-neutral-500 mt-2">Loading reviews...</p>
                      </div>
                    ) : reviews.length > 0 ? (
                      reviews.map((review) => (
                        <Card key={review._id} className="p-6">
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                                {review.user?.profileImage?.url ? (
                                  <img
                                    src={review.user.profileImage.url}
                                    alt={`${review.user.firstName} ${review.user.lastName}`}
                                    className="w-10 h-10 rounded-full object-cover"
                                  />
                                ) : (
                                  <span className="text-primary-600 font-medium">
                                    {review.isAnonymous
                                      ? 'A'
                                      : `${review.user?.firstName?.[0] || ''}${review.user?.lastName?.[0] || ''}`
                                    }
                                  </span>
                                )}
                              </div>
                              <div>
                                <h5 className="font-medium text-neutral-900">
                                  {review.isAnonymous
                                    ? 'Anonymous'
                                    : `${review.user?.firstName || ''} ${review.user?.lastName || ''}`
                                  }
                                </h5>
                                <div className="flex items-center gap-2">
                                  <div className="flex items-center">
                                    {[1, 2, 3, 4, 5].map((star) => (
                                      <Star
                                        key={star}
                                        className={`w-4 h-4 ${
                                          star <= review.rating ? 'text-yellow-400 fill-current' : 'text-neutral-300'
                                        }`}
                                      />
                                    ))}
                                  </div>
                                  <span className="text-sm text-neutral-500">
                                    {new Date(review.createdAt).toLocaleDateString()}
                                  </span>
                                </div>
                              </div>
                            </div>
                            {review.isVerified && <Badge variant="success">Verified</Badge>}
                          </div>
                          <p className="text-neutral-600 mb-4">{review.review}</p>
                          <div className="flex items-center gap-4 text-sm text-neutral-500">
                            <button
                              onClick={() => handleMarkHelpful(review._id)}
                              className="flex items-center gap-1 hover:text-primary-600 transition-colors"
                            >
                              <ThumbsUp className="w-4 h-4" />
                              Helpful ({review.helpfulVotes || 0})
                            </button>
                          </div>
                        </Card>
                      ))
                    ) : (
                      <div className="text-center py-12 text-neutral-500">
                        <MessageCircle className="w-12 h-12 mx-auto mb-4 text-neutral-300" />
                        <p className="text-lg font-medium mb-2">No reviews yet</p>
                        <p>Be the first to share your experience!</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Booking Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-6 transition-all duration-300 ease-in-out">
              <Card className="p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-3xl font-bold text-primary-600">
                        {formatPrice(tour.price)}
                      </span>
                      {tour.originalPrice && tour.originalPrice > tour.price && (
                        <span className="text-lg text-neutral-500 line-through">
                          {formatPrice(tour.originalPrice)}
                        </span>
                      )}
                    </div>
                    {discountPercentage > 0 && (
                      <Badge variant="danger">
                        Save {discountPercentage}%
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-neutral-500">per person</p>
                </div>

                {/* Next Available Date */}
                {tour.nextAvailableDate && (
                  <div className="mb-6 p-4 bg-accent-50 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Calendar className="w-4 h-4 text-accent-600 mr-2" />
                      <span className="font-medium text-accent-800">Next Available</span>
                    </div>
                    <p className="text-accent-700">
                      {new Date(tour.nextAvailableDate.startDate).toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </p>
                    <p className="text-sm text-accent-600 mt-1">
                      {tour.nextAvailableDate.availableSpots} spots remaining
                    </p>
                  </div>
                )}

                <Button
                  variant="primary"
                  size="lg"
                  fullWidth
                  onClick={handleBookNow}
                  className="mb-4"
                >
                  Book Now
                </Button>

                <div className="text-center text-sm text-neutral-500 mb-6">
                  Free cancellation up to 24 hours before
                </div>

                {/* Tour Provider */}
                {tour.provider && (
                  <div className="border-t pt-6">
                    <h4 className="font-semibold mb-3">Tour Provider</h4>
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-neutral-200 rounded-full flex items-center justify-center mr-3">
                        {tour.provider.profileImage?.url ? (
                          <img
                            src={tour.provider.profileImage.url}
                            alt={tour.provider.fullName}
                            className="w-12 h-12 rounded-full object-cover"
                          />
                        ) : (
                          <span className="text-neutral-600 font-medium">
                            {tour.provider.firstName?.[0]}{tour.provider.lastName?.[0]}
                          </span>
                        )}
                      </div>
                      <div>
                        <p className="font-medium">
                          {tour.provider.companyInfo?.companyName ||
                           `${tour.provider.firstName} ${tour.provider.lastName}`}
                        </p>
                        <p className="text-sm text-neutral-500">Verified Provider</p>
                      </div>
                    </div>
                  </div>
                )}
              </Card>
            </div>
          </div>
        </div>

        {/* Similar Tours */}
        {similarTours.length > 0 && (
          <div className="mt-16">
            <h2 className="text-2xl font-bold text-neutral-900 mb-8">
              Similar Tours You Might Like
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {similarTours.map((similarTour) => (
                <TourCard key={similarTour._id} tour={similarTour} />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TourDetailPage;
