const express = require('express');
const router = express.Router();

// Temporary placeholder routes
router.get('/dashboard', (req, res) => {
  res.status(501).json({
    success: false,
    message: 'Admin dashboard endpoint not implemented yet'
  });
});

router.get('/users', (req, res) => {
  res.status(501).json({
    success: false,
    message: 'Get all users endpoint not implemented yet'
  });
});

router.get('/tours', (req, res) => {
  res.status(501).json({
    success: false,
    message: 'Admin get all tours endpoint not implemented yet'
  });
});

router.get('/bookings', (req, res) => {
  res.status(501).json({
    success: false,
    message: 'Admin get all bookings endpoint not implemented yet'
  });
});

module.exports = router;
