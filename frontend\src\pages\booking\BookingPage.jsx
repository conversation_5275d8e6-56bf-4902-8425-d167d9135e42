import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Shield, Clock, Users } from 'lucide-react';
import { tourAPI, bookingAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import BookingForm from '../../components/booking/BookingForm';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { PageLoader } from '../../components/ui/LoadingSpinner';
import toast from 'react-hot-toast';

const BookingPage = () => {
  const { tourId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [tour, setTour] = useState(null);
  const [loading, setLoading] = useState(true);
  const [bookingLoading, setBookingLoading] = useState(false);

  useEffect(() => {
    fetchTourDetails();
  }, [tourId]);

  const fetchTourDetails = async () => {
    try {
      setLoading(true);
      const response = await tourAPI.getTour(tourId);
      setTour(response.data.data);
    } catch (error) {
      console.error('Error fetching tour details:', error);
      toast.error('Failed to load tour details');
      navigate('/tours');
    } finally {
      setLoading(false);
    }
  };

  const handleBookingSubmit = async (bookingData) => {
    try {
      setBookingLoading(true);
      const response = await bookingAPI.createBooking(bookingData);
      const booking = response.data.data;

      toast.success('Booking created successfully!');
      navigate(`/payment/${booking._id}`);
    } catch (error) {
      console.error('Error creating booking:', error);
      toast.error(error.response?.data?.message || 'Failed to create booking');
    } finally {
      setBookingLoading(false);
    }
  };

  if (loading) {
    return <PageLoader message="Loading booking details..." />;
  }

  if (!tour) {
    return (
      <div className="container-custom py-12 text-center">
        <h1 className="text-2xl font-bold text-neutral-900 mb-4">Tour not found</h1>
        <Button onClick={() => navigate('/tours')}>
          Back to Tours
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-neutral-50">
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Button
            variant="ghost"
            icon={ArrowLeft}
            onClick={() => navigate(`/tours/${tour.slug || tour._id}`)}
            className="mb-4"
          >
            Back to Tour Details
          </Button>

          <h1 className="text-3xl font-bold text-neutral-900 mb-2">
            Book Your Adventure
          </h1>
          <p className="text-neutral-600">
            Complete your booking for {tour.name}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Booking Form */}
          <div className="lg:col-span-2">
            <BookingForm
              tour={tour}
              onSubmit={handleBookingSubmit}
              loading={bookingLoading}
            />
          </div>

          {/* Tour Summary Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-6 space-y-6">
              {/* Tour Info Card */}
              <Card className="p-6">
                <div className="mb-4">
                  <img
                    src={tour.images?.find(img => img.isMain)?.url || tour.images?.[0]?.url}
                    alt={tour.name}
                    className="w-full h-48 object-cover rounded-lg"
                  />
                </div>

                <h3 className="text-xl font-semibold mb-2">{tour.name}</h3>
                <p className="text-neutral-600 mb-4">
                  {tour.destination.city}, {tour.destination.country}
                </p>

                <div className="space-y-3 text-sm">
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 text-neutral-500 mr-2" />
                    <span>{tour.duration.days} days, {tour.duration.nights} nights</span>
                  </div>
                  <div className="flex items-center">
                    <Users className="w-4 h-4 text-neutral-500 mr-2" />
                    <span>Max {tour.maxGroupSize} people</span>
                  </div>
                  <div className="flex items-center">
                    <Shield className="w-4 h-4 text-neutral-500 mr-2" />
                    <span>Free cancellation up to 24 hours</span>
                  </div>
                </div>
              </Card>

              {/* Security & Trust */}
              <Card className="p-6">
                <h4 className="font-semibold mb-4">Your Booking is Protected</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex items-start">
                    <Shield className="w-4 h-4 text-accent-500 mr-2 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium">Secure Payment</p>
                      <p className="text-neutral-600">Your payment information is encrypted and secure</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Shield className="w-4 h-4 text-accent-500 mr-2 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium">Instant Confirmation</p>
                      <p className="text-neutral-600">Receive confirmation immediately after booking</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Shield className="w-4 h-4 text-accent-500 mr-2 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium">24/7 Support</p>
                      <p className="text-neutral-600">Get help whenever you need it</p>
                    </div>
                  </div>
                </div>
              </Card>

              {/* Contact Support */}
              <Card className="p-6">
                <h4 className="font-semibold mb-4">Need Help?</h4>
                <p className="text-sm text-neutral-600 mb-4">
                  Our travel experts are here to help you with your booking.
                </p>
                <div className="space-y-2 text-sm">
                  <p><strong>Phone:</strong> +****************</p>
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Hours:</strong> 24/7 Support</p>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingPage;
