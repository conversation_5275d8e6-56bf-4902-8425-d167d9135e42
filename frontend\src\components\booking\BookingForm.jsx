import React, { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Calendar, Users, Plus, Minus, User, Phone, Mail } from 'lucide-react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import Card from '../ui/Card';
import Badge from '../ui/Badge';

// Validation schema
const bookingSchema = yup.object({
  tourDate: yup.object({
    startDate: yup.date().required('Start date is required'),
    endDate: yup.date().required('End date is required'),
  }),
  participants: yup.object({
    adults: yup.number().min(1, 'At least one adult is required').required(),
    children: yup.number().min(0).required(),
    infants: yup.number().min(0).required(),
  }),
  participantDetails: yup.array().of(
    yup.object({
      firstName: yup.string().required('First name is required'),
      lastName: yup.string().required('Last name is required'),
      dateOfBirth: yup.date().nullable(),
      gender: yup.string().oneOf(['male', 'female', 'other']).nullable(),
      passportNumber: yup.string().nullable(),
      nationality: yup.string().nullable(),
      dietaryRequirements: yup.string().nullable(),
      medicalConditions: yup.string().nullable(),
      emergencyContact: yup.object({
        name: yup.string().nullable(),
        phone: yup.string().nullable(),
        relationship: yup.string().nullable(),
      }).nullable(),
    })
  ),
  specialRequests: yup.string().nullable(),
});

const BookingForm = ({ 
  tour, 
  onSubmit, 
  loading = false,
  selectedDate = null,
  className = '' 
}) => {
  const [step, setStep] = useState(1);
  const [pricing, setPricing] = useState({
    subtotal: 0,
    taxes: 0,
    fees: 25,
    total: 0
  });

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(bookingSchema),
    defaultValues: {
      tourDate: {
        startDate: selectedDate?.startDate || '',
        endDate: selectedDate?.endDate || '',
      },
      participants: {
        adults: 1,
        children: 0,
        infants: 0,
      },
      participantDetails: [
        {
          firstName: '',
          lastName: '',
          dateOfBirth: null,
          gender: '',
          passportNumber: '',
          nationality: '',
          dietaryRequirements: '',
          medicalConditions: '',
          emergencyContact: {
            name: '',
            phone: '',
            relationship: '',
          },
        }
      ],
      specialRequests: '',
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'participantDetails',
  });

  const watchedParticipants = watch('participants');
  const totalParticipants = watchedParticipants.adults + watchedParticipants.children + watchedParticipants.infants;

  // Update participant details array when participant count changes
  useEffect(() => {
    const currentCount = fields.length;
    const requiredCount = totalParticipants;

    if (requiredCount > currentCount) {
      // Add new participant fields
      for (let i = currentCount; i < requiredCount; i++) {
        append({
          firstName: '',
          lastName: '',
          dateOfBirth: null,
          gender: '',
          passportNumber: '',
          nationality: '',
          dietaryRequirements: '',
          medicalConditions: '',
          emergencyContact: {
            name: '',
            phone: '',
            relationship: '',
          },
        });
      }
    } else if (requiredCount < currentCount) {
      // Remove excess participant fields
      for (let i = currentCount - 1; i >= requiredCount; i--) {
        remove(i);
      }
    }
  }, [totalParticipants, fields.length, append, remove]);

  // Calculate pricing
  useEffect(() => {
    const adultPrice = tour.price;
    const childPrice = tour.price * 0.7; // 30% discount for children
    const infantPrice = 0; // Infants are free

    const subtotal = (watchedParticipants.adults * adultPrice) + 
                    (watchedParticipants.children * childPrice) + 
                    (watchedParticipants.infants * infantPrice);
    
    const taxes = subtotal * 0.1; // 10% tax
    const fees = 25; // Service fee
    const total = subtotal + taxes + fees;

    setPricing({
      subtotal,
      taxes,
      fees,
      total
    });
  }, [watchedParticipants, tour.price]);

  const updateParticipantCount = (type, increment) => {
    const current = watchedParticipants[type];
    const newValue = increment ? current + 1 : Math.max(type === 'adults' ? 1 : 0, current - 1);
    setValue(`participants.${type}`, newValue);
  };

  const formatPrice = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: tour.currency || 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const handleFormSubmit = (data) => {
    const bookingData = {
      ...data,
      pricing,
      tourId: tour._id,
    };
    onSubmit(bookingData);
  };

  const steps = [
    { id: 1, title: 'Date & Guests', description: 'Select your travel dates and number of guests' },
    { id: 2, title: 'Traveler Details', description: 'Provide information for all travelers' },
    { id: 3, title: 'Review & Book', description: 'Review your booking and confirm' },
  ];

  return (
    <div className={className}>
      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((stepItem, index) => (
            <div key={stepItem.id} className="flex items-center">
              <div className={`
                flex items-center justify-center w-10 h-10 rounded-full border-2 
                ${step >= stepItem.id 
                  ? 'bg-primary-600 border-primary-600 text-white' 
                  : 'border-neutral-300 text-neutral-500'
                }
              `}>
                {stepItem.id}
              </div>
              <div className="ml-3 hidden sm:block">
                <p className={`text-sm font-medium ${
                  step >= stepItem.id ? 'text-primary-600' : 'text-neutral-500'
                }`}>
                  {stepItem.title}
                </p>
                <p className="text-xs text-neutral-500">{stepItem.description}</p>
              </div>
              {index < steps.length - 1 && (
                <div className={`
                  hidden sm:block w-16 h-0.5 ml-6 
                  ${step > stepItem.id ? 'bg-primary-600' : 'bg-neutral-300'}
                `} />
              )}
            </div>
          ))}
        </div>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)}>
        {/* Step 1: Date & Guests */}
        {step === 1 && (
          <Card className="p-6">
            <h3 className="text-xl font-semibold mb-6">Select Date & Guests</h3>
            
            {/* Date Selection */}
            <div className="mb-6">
              <h4 className="font-medium mb-4">Travel Dates</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Start Date"
                  type="date"
                  icon={Calendar}
                  error={errors.tourDate?.startDate?.message}
                  {...register('tourDate.startDate')}
                  required
                />
                <Input
                  label="End Date"
                  type="date"
                  icon={Calendar}
                  error={errors.tourDate?.endDate?.message}
                  {...register('tourDate.endDate')}
                  required
                />
              </div>
            </div>

            {/* Guest Selection */}
            <div className="mb-6">
              <h4 className="font-medium mb-4">Number of Guests</h4>
              <div className="space-y-4">
                {/* Adults */}
                <div className="flex items-center justify-between p-4 border border-neutral-200 rounded-lg">
                  <div>
                    <p className="font-medium">Adults</p>
                    <p className="text-sm text-neutral-500">Age 18+</p>
                  </div>
                  <div className="flex items-center space-x-3">
                    <button
                      type="button"
                      onClick={() => updateParticipantCount('adults', false)}
                      className="w-8 h-8 rounded-full border border-neutral-300 flex items-center justify-center hover:bg-neutral-50"
                      disabled={watchedParticipants.adults <= 1}
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <span className="w-8 text-center font-medium">
                      {watchedParticipants.adults}
                    </span>
                    <button
                      type="button"
                      onClick={() => updateParticipantCount('adults', true)}
                      className="w-8 h-8 rounded-full border border-neutral-300 flex items-center justify-center hover:bg-neutral-50"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Children */}
                <div className="flex items-center justify-between p-4 border border-neutral-200 rounded-lg">
                  <div>
                    <p className="font-medium">Children</p>
                    <p className="text-sm text-neutral-500">Age 2-17</p>
                  </div>
                  <div className="flex items-center space-x-3">
                    <button
                      type="button"
                      onClick={() => updateParticipantCount('children', false)}
                      className="w-8 h-8 rounded-full border border-neutral-300 flex items-center justify-center hover:bg-neutral-50"
                      disabled={watchedParticipants.children <= 0}
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <span className="w-8 text-center font-medium">
                      {watchedParticipants.children}
                    </span>
                    <button
                      type="button"
                      onClick={() => updateParticipantCount('children', true)}
                      className="w-8 h-8 rounded-full border border-neutral-300 flex items-center justify-center hover:bg-neutral-50"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Infants */}
                <div className="flex items-center justify-between p-4 border border-neutral-200 rounded-lg">
                  <div>
                    <p className="font-medium">Infants</p>
                    <p className="text-sm text-neutral-500">Under 2 (Free)</p>
                  </div>
                  <div className="flex items-center space-x-3">
                    <button
                      type="button"
                      onClick={() => updateParticipantCount('infants', false)}
                      className="w-8 h-8 rounded-full border border-neutral-300 flex items-center justify-center hover:bg-neutral-50"
                      disabled={watchedParticipants.infants <= 0}
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <span className="w-8 text-center font-medium">
                      {watchedParticipants.infants}
                    </span>
                    <button
                      type="button"
                      onClick={() => updateParticipantCount('infants', true)}
                      className="w-8 h-8 rounded-full border border-neutral-300 flex items-center justify-center hover:bg-neutral-50"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Price Summary */}
            <div className="bg-neutral-50 p-4 rounded-lg mb-6">
              <h4 className="font-medium mb-3">Price Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Subtotal ({totalParticipants} guests)</span>
                  <span>{formatPrice(pricing.subtotal)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Taxes & Fees</span>
                  <span>{formatPrice(pricing.taxes + pricing.fees)}</span>
                </div>
                <div className="border-t pt-2 flex justify-between font-semibold">
                  <span>Total</span>
                  <span>{formatPrice(pricing.total)}</span>
                </div>
              </div>
            </div>

            <Button
              type="button"
              variant="primary"
              fullWidth
              onClick={() => setStep(2)}
            >
              Continue to Traveler Details
            </Button>
          </Card>
        )}

        {/* Step 2: Traveler Details */}
        {step === 2 && (
          <Card className="p-6">
            <h3 className="text-xl font-semibold mb-6">Traveler Information</h3>
            
            <div className="space-y-6">
              {fields.map((field, index) => (
                <div key={field.id} className="border border-neutral-200 rounded-lg p-4">
                  <h4 className="font-medium mb-4">
                    Traveler {index + 1}
                    {index === 0 && <Badge variant="primary" className="ml-2">Lead Traveler</Badge>}
                  </h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="First Name"
                      placeholder="John"
                      error={errors.participantDetails?.[index]?.firstName?.message}
                      {...register(`participantDetails.${index}.firstName`)}
                      required
                    />
                    <Input
                      label="Last Name"
                      placeholder="Doe"
                      error={errors.participantDetails?.[index]?.lastName?.message}
                      {...register(`participantDetails.${index}.lastName`)}
                      required
                    />
                    <Input
                      label="Date of Birth"
                      type="date"
                      error={errors.participantDetails?.[index]?.dateOfBirth?.message}
                      {...register(`participantDetails.${index}.dateOfBirth`)}
                    />
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-1">
                        Gender
                      </label>
                      <select
                        className="input w-full"
                        {...register(`participantDetails.${index}.gender`)}
                      >
                        <option value="">Select Gender</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <Input
                      label="Passport Number"
                      placeholder="Optional"
                      {...register(`participantDetails.${index}.passportNumber`)}
                    />
                    <Input
                      label="Nationality"
                      placeholder="e.g., American"
                      {...register(`participantDetails.${index}.nationality`)}
                    />
                  </div>
                  
                  <div className="mt-4">
                    <Input
                      label="Dietary Requirements"
                      placeholder="Any dietary restrictions or preferences"
                      {...register(`participantDetails.${index}.dietaryRequirements`)}
                    />
                  </div>
                  
                  <div className="mt-4">
                    <Input
                      label="Medical Conditions"
                      placeholder="Any medical conditions we should know about"
                      {...register(`participantDetails.${index}.medicalConditions`)}
                    />
                  </div>
                  
                  {index === 0 && (
                    <div className="mt-4">
                      <h5 className="font-medium mb-3">Emergency Contact</h5>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Input
                          label="Name"
                          placeholder="Emergency contact name"
                          {...register(`participantDetails.${index}.emergencyContact.name`)}
                        />
                        <Input
                          label="Phone"
                          placeholder="Emergency contact phone"
                          {...register(`participantDetails.${index}.emergencyContact.phone`)}
                        />
                        <Input
                          label="Relationship"
                          placeholder="e.g., Spouse, Parent"
                          {...register(`participantDetails.${index}.emergencyContact.relationship`)}
                        />
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            <div className="mt-6">
              <Input
                label="Special Requests"
                placeholder="Any special requests or notes for your tour"
                {...register('specialRequests')}
              />
            </div>

            <div className="flex gap-4 mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => setStep(1)}
              >
                Back
              </Button>
              <Button
                type="button"
                variant="primary"
                fullWidth
                onClick={() => setStep(3)}
              >
                Review Booking
              </Button>
            </div>
          </Card>
        )}

        {/* Step 3: Review & Book */}
        {step === 3 && (
          <Card className="p-6">
            <h3 className="text-xl font-semibold mb-6">Review Your Booking</h3>
            
            {/* Tour Summary */}
            <div className="mb-6 p-4 bg-neutral-50 rounded-lg">
              <h4 className="font-medium mb-2">{tour.name}</h4>
              <p className="text-sm text-neutral-600 mb-2">
                {tour.destination.city}, {tour.destination.country}
              </p>
              <p className="text-sm text-neutral-600">
                {tour.duration.days} days, {tour.duration.nights} nights
              </p>
            </div>

            {/* Booking Summary */}
            <div className="mb-6">
              <h4 className="font-medium mb-3">Booking Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Dates:</span>
                  <span>{watch('tourDate.startDate')} to {watch('tourDate.endDate')}</span>
                </div>
                <div className="flex justify-between">
                  <span>Guests:</span>
                  <span>{totalParticipants} travelers</span>
                </div>
              </div>
            </div>

            {/* Final Price */}
            <div className="mb-6 p-4 border border-primary-200 bg-primary-50 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold">Total Amount:</span>
                <span className="text-2xl font-bold text-primary-600">
                  {formatPrice(pricing.total)}
                </span>
              </div>
            </div>

            <div className="flex gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setStep(2)}
              >
                Back
              </Button>
              <Button
                type="submit"
                variant="primary"
                fullWidth
                loading={loading}
              >
                Confirm Booking
              </Button>
            </div>
          </Card>
        )}
      </form>
    </div>
  );
};

export default BookingForm;
