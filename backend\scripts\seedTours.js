const mongoose = require('mongoose');
const Tour = require('../models/Tour');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/wanderlust')
  .then(() => console.log('✅ Connected to MongoDB'))
  .catch((error) => console.error('❌ MongoDB connection error:', error));

const sampleTours = [
  {
    name: "Magical Bali Adventure",
    slug: "magical-bali-adventure",
    description: "Discover the enchanting beauty of Bali with this comprehensive 7-day adventure. Visit ancient temples, pristine beaches, lush rice terraces, and experience the rich Balinese culture. This tour includes visits to Ubud's monkey forest, traditional villages, stunning waterfalls, and the famous Tanah Lot temple at sunset.",
    shortDescription: "7-day cultural and adventure tour through Bali's most beautiful destinations",
    price: 899,
    originalPrice: 1199,
    currency: "USD",
    duration: {
      days: 7,
      nights: 6
    },
    maxGroupSize: 12,
    difficulty: "moderate",
    category: "cultural",
    destination: {
      country: "Indonesia",
      city: "Bali",
      coordinates: {
        latitude: -8.3405,
        longitude: 115.0920
      }
    },
    images: [
      {
        public_id: "bali-temple-1",
        url: "https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?w=800",
        caption: "Bali Temple",
        isMain: true
      },
      {
        public_id: "bali-rice-terraces-1",
        url: "https://images.unsplash.com/photo-1518548419970-58e3b4079ab2?w=800",
        caption: "Bali Rice Terraces"
      }
    ],
    cancellationPolicy: "Free cancellation up to 24 hours before the tour starts. 50% refund for cancellations made 24-48 hours before. No refund for cancellations made less than 24 hours before the tour.",
    included: [
      "6 nights accommodation in 4-star hotels",
      "Daily breakfast and 4 dinners",
      "Professional English-speaking guide",
      "All entrance fees to attractions",
      "Airport transfers",
      "Transportation in air-conditioned vehicle"
    ],
    excluded: [
      "International flights",
      "Travel insurance",
      "Personal expenses",
      "Lunches (except specified)",
      "Tips and gratuities"
    ],
    itinerary: [
      {
        day: 1,
        title: "Arrival in Denpasar",
        description: "Arrive at Ngurah Rai International Airport. Transfer to hotel in Ubud.",
        activities: ["Airport pickup", "Hotel check-in", "Welcome dinner"],
        meals: { dinner: true },
        accommodation: "Ubud Resort"
      },
      {
        day: 2,
        title: "Ubud Cultural Tour",
        description: "Explore Ubud's cultural highlights including monkey forest and traditional markets.",
        activities: ["Sacred Monkey Forest", "Ubud Traditional Market", "Tegallalang Rice Terraces"],
        meals: { breakfast: true, dinner: true },
        accommodation: "Ubud Resort"
      }
    ],
    ratingsAverage: 4.8,
    ratingsQuantity: 127,
    isFeatured: true,
    isActive: true,
    nextAvailableDate: {
      startDate: new Date('2024-03-15'),
      endDate: new Date('2024-03-21'),
      availableSpots: 8
    }
  },
  {
    name: "Swiss Alps Hiking Experience",
    slug: "swiss-alps-hiking-experience",
    description: "Embark on an unforgettable hiking adventure through the majestic Swiss Alps. This 5-day tour takes you through some of Switzerland's most breathtaking mountain landscapes, charming alpine villages, and pristine lakes. Perfect for nature lovers and hiking enthusiasts.",
    shortDescription: "5-day hiking adventure through Switzerland's most beautiful alpine regions",
    price: 1299,
    originalPrice: 1599,
    currency: "USD",
    duration: {
      days: 5,
      nights: 4
    },
    maxGroupSize: 8,
    difficulty: "difficult",
    category: "adventure",
    destination: {
      country: "Switzerland",
      city: "Interlaken",
      coordinates: {
        latitude: 46.6863,
        longitude: 7.8632
      }
    },
    images: [
      {
        public_id: "swiss-alps-1",
        url: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800",
        caption: "Swiss Alps Mountain View",
        isMain: true
      },
      {
        public_id: "alpine-lake-1",
        url: "https://images.unsplash.com/photo-1531366936337-7c912a4589a7?w=800",
        caption: "Alpine Lake"
      }
    ],
    cancellationPolicy: "Free cancellation up to 48 hours before the tour starts. 25% refund for cancellations made 48-72 hours before. No refund for cancellations made less than 48 hours before the tour.",
    included: [
      "4 nights in mountain lodges",
      "All meals during the tour",
      "Professional mountain guide",
      "Hiking equipment rental",
      "Cable car tickets",
      "Group transportation"
    ],
    excluded: [
      "International flights",
      "Travel insurance",
      "Personal hiking gear",
      "Alcoholic beverages",
      "Souvenirs"
    ],
    itinerary: [
      {
        day: 1,
        title: "Arrival in Interlaken",
        description: "Meet the group and prepare for the hiking adventure.",
        activities: ["Group briefing", "Equipment check", "Easy acclimatization walk"],
        meals: { breakfast: true, lunch: true, dinner: true },
        accommodation: "Mountain Lodge"
      }
    ],
    ratingsAverage: 4.9,
    ratingsQuantity: 89,
    isFeatured: true,
    isActive: true,
    nextAvailableDate: {
      startDate: new Date('2024-04-10'),
      endDate: new Date('2024-04-14'),
      availableSpots: 5
    }
  },
  {
    name: "Tokyo Cultural Immersion",
    slug: "tokyo-cultural-immersion",
    description: "Dive deep into Japanese culture with this immersive 6-day Tokyo experience. From traditional temples to modern districts, authentic cuisine to pop culture, this tour offers a perfect blend of old and new Japan.",
    shortDescription: "6-day cultural journey through traditional and modern Tokyo",
    price: 1099,
    currency: "USD",
    duration: {
      days: 6,
      nights: 5
    },
    maxGroupSize: 10,
    difficulty: "easy",
    category: "cultural",
    destination: {
      country: "Japan",
      city: "Tokyo",
      coordinates: {
        latitude: 35.6762,
        longitude: 139.6503
      }
    },
    images: [
      {
        public_id: "tokyo-temple-1",
        url: "https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?w=800",
        caption: "Tokyo Temple",
        isMain: true
      }
    ],
    cancellationPolicy: "Free cancellation up to 24 hours before the tour starts. 50% refund for cancellations made 24-48 hours before. No refund for cancellations made less than 24 hours before the tour.",
    included: [
      "5 nights in traditional ryokan",
      "Daily breakfast and 3 traditional dinners",
      "English-speaking cultural guide",
      "All temple entrance fees",
      "Tokyo Metro passes",
      "Cultural workshops"
    ],
    excluded: [
      "International flights",
      "Travel insurance",
      "Lunches",
      "Personal shopping",
      "Optional activities"
    ],
    ratingsAverage: 4.7,
    ratingsQuantity: 156,
    isFeatured: false,
    isActive: true,
    nextAvailableDate: {
      startDate: new Date('2024-05-01'),
      endDate: new Date('2024-05-06'),
      availableSpots: 7
    }
  },
  {
    name: "Santorini Sunset Romance",
    slug: "santorini-sunset-romance",
    description: "Experience the romance of Santorini with this 4-day luxury getaway. Perfect for couples, this tour includes wine tasting, sunset cruises, and stays in cave hotels with caldera views.",
    shortDescription: "4-day romantic luxury experience in beautiful Santorini",
    price: 1599,
    currency: "USD",
    duration: {
      days: 4,
      nights: 3
    },
    maxGroupSize: 6,
    difficulty: "easy",
    category: "luxury",
    destination: {
      country: "Greece",
      city: "Santorini",
      coordinates: {
        latitude: 36.3932,
        longitude: 25.4615
      }
    },
    images: [
      {
        public_id: "santorini-sunset-1",
        url: "https://images.unsplash.com/photo-1570077188670-e3a8d69ac5ff?w=800",
        caption: "Santorini Sunset",
        isMain: true
      }
    ],
    cancellationPolicy: "Free cancellation up to 72 hours before the tour starts. 50% refund for cancellations made 72-96 hours before. No refund for cancellations made less than 72 hours before the tour.",
    included: [
      "3 nights in luxury cave hotel",
      "Daily breakfast",
      "Sunset cruise with dinner",
      "Wine tasting tour",
      "Private transfers",
      "Couples spa treatment"
    ],
    excluded: [
      "International flights",
      "Travel insurance",
      "Lunches",
      "Personal expenses",
      "Additional spa treatments"
    ],
    ratingsAverage: 4.9,
    ratingsQuantity: 78,
    isFeatured: true,
    isActive: true,
    nextAvailableDate: {
      startDate: new Date('2024-06-15'),
      endDate: new Date('2024-06-18'),
      availableSpots: 4
    }
  }
];

const seedTours = async () => {
  try {
    // Get the provider user
    const User = require('../models/User');
    const provider = await User.findOne({ role: 'tour-provider' });

    if (!provider) {
      console.error('❌ No tour provider found. Please run seedUsers.js first.');
      process.exit(1);
    }

    // Add provider to all tours
    const toursWithProvider = sampleTours.map(tour => ({
      ...tour,
      provider: provider._id
    }));

    // Clear existing tours
    await Tour.deleteMany({});
    console.log('🗑️  Cleared existing tours');

    // Insert sample tours
    const tours = await Tour.insertMany(toursWithProvider);
    console.log(`✅ Successfully seeded ${tours.length} tours`);

    // Display created tours
    tours.forEach(tour => {
      console.log(`   - ${tour.name} (${tour.slug})`);
    });

    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding tours:', error);
    process.exit(1);
  }
};

seedTours();
