import axios from 'axios';
import toast from 'react-hot-toast';

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const message = error.response?.data?.message || error.message || 'Something went wrong';
    
    // Handle specific error cases
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
      toast.error('Session expired. Please login again.');
    } else if (error.response?.status === 403) {
      toast.error('You do not have permission to perform this action.');
    } else if (error.response?.status === 404) {
      toast.error('Resource not found.');
    } else if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    } else {
      toast.error(message);
    }
    
    return Promise.reject(error);
  }
);

// API endpoints
export const authAPI = {
  register: (data) => api.post('/auth/register', data),
  login: (data) => api.post('/auth/login', data),
  logout: () => api.post('/auth/logout'),
  getProfile: () => api.get('/auth/me'),
  updateProfile: (data) => api.put('/auth/update-details', data),
  updatePassword: (data) => api.put('/auth/update-password', data),
  forgotPassword: (data) => api.post('/auth/forgot-password', data),
  resetPassword: (token, data) => api.put(`/auth/reset-password/${token}`, data),
};

export const userAPI = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (data) => api.put('/users/profile', data),
  uploadProfileImage: (formData) => api.post('/users/profile/image', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  getBookings: (params) => api.get('/users/bookings', { params }),
  getBooking: (id) => api.get(`/users/bookings/${id}`),
  cancelBooking: (id, data) => api.put(`/users/bookings/${id}/cancel`, data),
  deleteAccount: () => api.delete('/users/account'),
};

export const tourAPI = {
  getAllTours: (params) => api.get('/tours', { params }),
  getTour: (id) => api.get(`/tours/${id}`),
  getFeaturedTours: (params) => api.get('/tours/featured', { params }),
  getTrendingTours: (params) => api.get('/tours/trending', { params }),
  getSimilarTours: (id, params) => api.get(`/tours/${id}/similar`, { params }),
  getRecommendations: (params) => api.get('/tours/recommendations', { params }),
  getTourStats: () => api.get('/tours/stats'),
  createTour: (data) => api.post('/tours', data),
  updateTour: (id, data) => api.put(`/tours/${id}`, data),
  deleteTour: (id) => api.delete(`/tours/${id}`),
  uploadTourImages: (id, formData) => api.post(`/tours/${id}/images`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  deleteTourImage: (tourId, imageId) => api.delete(`/tours/${tourId}/images/${imageId}`),
};

export const bookingAPI = {
  createBooking: (data) => api.post('/bookings', data),
  getAllBookings: (params) => api.get('/bookings', { params }),
  getBooking: (id) => api.get(`/bookings/${id}`),
  updateBookingStatus: (id, data) => api.put(`/bookings/${id}/status`, data),
  cancelBooking: (id, data) => api.put(`/bookings/${id}/cancel`, data),
  processPayment: (id, data) => api.post(`/bookings/${id}/payment`, data),
  createPaymentIntent: (id) => api.post(`/bookings/${id}/payment-intent`),
  processRefund: (id, data) => api.post(`/bookings/${id}/refund`, data),
  getPaymentMethods: () => api.get('/bookings/payment-methods'),
  getBookingStats: () => api.get('/bookings/stats'),
};

export const uploadAPI = {
  uploadImage: (formData) => api.post('/upload/image', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  uploadImages: (formData) => api.post('/upload/images', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  deleteImage: (publicId) => api.delete(`/upload/image/${publicId}`),
  getUploadConfig: () => api.get('/upload/config'),
  generateSignedUrl: (data) => api.post('/upload/signed-url', data),
  processImage: (data) => api.post('/upload/process', data),
  getImageMetadata: (publicId) => api.get(`/upload/metadata/${publicId}`),
};

export const adminAPI = {
  getDashboard: () => api.get('/admin/dashboard'),
  getAllUsers: (params) => api.get('/admin/users', { params }),
  getUser: (id) => api.get(`/admin/users/${id}`),
  updateUser: (id, data) => api.put(`/admin/users/${id}`, data),
  deleteUser: (id) => api.delete(`/admin/users/${id}`),
  getAllTours: (params) => api.get('/admin/tours', { params }),
  getAllBookings: (params) => api.get('/admin/bookings', { params }),
  getSystemStats: () => api.get('/admin/stats'),
};

// Utility functions
export const handleApiError = (error) => {
  const message = error.response?.data?.message || error.message || 'Something went wrong';
  console.error('API Error:', error);
  return message;
};

export const isAuthenticated = () => {
  return !!localStorage.getItem('token');
};

export const getAuthToken = () => {
  return localStorage.getItem('token');
};

export const setAuthToken = (token) => {
  localStorage.setItem('token', token);
};

export const removeAuthToken = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
};

export const getCurrentUser = () => {
  const user = localStorage.getItem('user');
  return user ? JSON.parse(user) : null;
};

export const setCurrentUser = (user) => {
  localStorage.setItem('user', JSON.stringify(user));
};

export default api;
