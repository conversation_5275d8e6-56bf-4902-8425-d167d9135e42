const express = require('express');
const router = express.Router();

// Import controllers
const {
  uploadSingleImage,
  uploadMultipleImagesController,
  deleteImageController,
  getUploadConfig,
  generateSignedUploadUrl,
  processImage,
  getImageMetadata
} = require('../controllers/uploadController');

const { protect, authorize } = require('../middleware/auth');
const { 
  uploadSingle, 
  uploadMultiple, 
  handleMulterError 
} = require('../middleware/upload');

// All routes require authentication
router.use(protect);

// Upload configuration
router.get('/config', getUploadConfig);

// Single image upload
router.post('/image', uploadSingle, handleMulterError, uploadSingleImage);

// Multiple images upload
router.post('/images', uploadMultiple, handleMulterError, uploadMultipleImagesController);

// Delete image
router.delete('/image/:publicId', deleteImageController);

// Advanced upload features
router.post('/signed-url', generateSignedUploadUrl);
router.post('/process', processImage);
router.get('/metadata/:publicId', getImageMetadata);

module.exports = router;
