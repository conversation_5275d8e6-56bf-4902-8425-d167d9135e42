class APIFeatures {
  constructor(query, queryString) {
    this.query = query;
    this.queryString = queryString;
  }

  // Search functionality
  search() {
    const keyword = this.queryString.keyword
      ? {
          $or: [
            { name: { $regex: this.queryString.keyword, $options: 'i' } },
            { description: { $regex: this.queryString.keyword, $options: 'i' } },
            { shortDescription: { $regex: this.queryString.keyword, $options: 'i' } },
            { 'destination.city': { $regex: this.queryString.keyword, $options: 'i' } },
            { 'destination.country': { $regex: this.queryString.keyword, $options: 'i' } },
            { category: { $regex: this.queryString.keyword, $options: 'i' } }
          ]
        }
      : {};

    this.query = this.query.find({ ...keyword });
    return this;
  }

  // Filter functionality
  filter() {
    const queryCopy = { ...this.queryString };

    // Remove fields that are not for filtering
    const removeFields = ['keyword', 'page', 'limit', 'sort', 'fields'];
    removeFields.forEach(key => delete queryCopy[key]);

    // Handle special filters
    const filterObj = {};

    // Destination filtering (city or country)
    if (queryCopy.destination) {
      filterObj.$or = [
        { 'destination.city': { $regex: queryCopy.destination, $options: 'i' } },
        { 'destination.country': { $regex: queryCopy.destination, $options: 'i' } }
      ];
      delete queryCopy.destination;
    }

    // Guest filtering (maxGroupSize should be >= requested guests)
    if (queryCopy.guests) {
      filterObj.maxGroupSize = { $gte: parseInt(queryCopy.guests) };
      delete queryCopy.guests;
    }

    // Price range filtering
    if (queryCopy.minPrice) {
      filterObj.price = { ...filterObj.price, $gte: parseFloat(queryCopy.minPrice) };
      delete queryCopy.minPrice;
    }
    if (queryCopy.maxPrice) {
      filterObj.price = { ...filterObj.price, $lte: parseFloat(queryCopy.maxPrice) };
      delete queryCopy.maxPrice;
    }

    // Duration filtering
    if (queryCopy.duration) {
      const [min, max] = queryCopy.duration.split('-');
      if (max === '+') {
        filterObj['duration.days'] = { $gte: parseInt(min) };
      } else {
        filterObj['duration.days'] = {
          $gte: parseInt(min),
          $lte: parseInt(max)
        };
      }
      delete queryCopy.duration;
    }

    // Advanced filtering for remaining fields
    let queryStr = JSON.stringify(queryCopy);
    queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, match => `$${match}`);
    const remainingFilters = JSON.parse(queryStr);

    // Combine all filters
    const finalFilter = { ...filterObj, ...remainingFilters };

    this.query = this.query.find(finalFilter);
    return this;
  }

  // Sorting functionality
  sort() {
    if (this.queryString.sort) {
      let sortBy;

      // Handle frontend sort options
      switch (this.queryString.sort) {
        case 'featured':
          sortBy = '-isFeatured -ratingsAverage';
          break;
        case 'price-asc':
          sortBy = 'price';
          break;
        case 'price-desc':
          sortBy = '-price';
          break;
        case 'rating':
          sortBy = '-ratingsAverage -ratingsQuantity';
          break;
        case 'newest':
          sortBy = '-createdAt';
          break;
        case 'popular':
          sortBy = '-ratingsQuantity -ratingsAverage';
          break;
        case 'duration-asc':
          sortBy = 'duration.days';
          break;
        case 'duration-desc':
          sortBy = '-duration.days';
          break;
        default:
          // If it's already in MongoDB format, use as is
          sortBy = this.queryString.sort.split(',').join(' ');
      }

      this.query = this.query.sort(sortBy);
    } else {
      // Default sort: featured first, then by rating
      this.query = this.query.sort('-isFeatured -ratingsAverage');
    }
    return this;
  }

  // Field limiting
  limitFields() {
    if (this.queryString.fields) {
      const fields = this.queryString.fields.split(',').join(' ');
      this.query = this.query.select(fields);
    } else {
      // Exclude __v field by default
      this.query = this.query.select('-__v');
    }
    return this;
  }

  // Pagination
  paginate() {
    const page = parseInt(this.queryString.page, 10) || 1;
    const limit = parseInt(this.queryString.limit, 10) || 10;
    const skip = (page - 1) * limit;

    this.query = this.query.skip(skip).limit(limit);
    return this;
  }

  // Get pagination info
  async getPaginationInfo() {
    const page = parseInt(this.queryString.page, 10) || 1;
    const limit = parseInt(this.queryString.limit, 10) || 10;
    
    // Clone the query to count total documents
    const countQuery = this.query.model.find(this.query.getQuery());
    const total = await countQuery.countDocuments();
    
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return {
      currentPage: page,
      totalPages,
      totalResults: total,
      resultsPerPage: limit,
      hasNextPage,
      hasPrevPage,
      nextPage: hasNextPage ? page + 1 : null,
      prevPage: hasPrevPage ? page - 1 : null
    };
  }
}

module.exports = APIFeatures;
