import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {
  User, Mail, Phone, Calendar, MapPin, Camera,
  Edit3, Save, X, Lock, Settings, Bell, Shield
} from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { userAPI } from '../../services/api';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Card from '../../components/ui/Card';
import Badge from '../../components/ui/Badge';
import Modal from '../../components/ui/Modal';
import toast from 'react-hot-toast';

// Validation schemas
const profileSchema = yup.object({
  firstName: yup.string().required('First name is required'),
  lastName: yup.string().required('Last name is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  phone: yup.string().nullable(),
  dateOfBirth: yup.date().nullable(),
  gender: yup.string().oneOf(['male', 'female', 'other']).nullable(),
  address: yup.object({
    street: yup.string().nullable(),
    city: yup.string().nullable(),
    state: yup.string().nullable(),
    country: yup.string().nullable(),
    zipCode: yup.string().nullable(),
  }).nullable(),
});

const passwordSchema = yup.object({
  currentPassword: yup.string().required('Current password is required'),
  newPassword: yup.string()
    .min(6, 'Password must be at least 6 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    )
    .required('New password is required'),
  confirmPassword: yup.string()
    .oneOf([yup.ref('newPassword')], 'Passwords must match')
    .required('Please confirm your password'),
});

const ProfilePage = () => {
  const { user, updateUser } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [passwordLoading, setPasswordLoading] = useState(false);

  const {
    register: registerProfile,
    handleSubmit: handleProfileSubmit,
    formState: { errors: profileErrors },
    reset: resetProfile,
  } = useForm({
    resolver: yupResolver(profileSchema),
    defaultValues: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      phone: user?.phone || '',
      dateOfBirth: user?.dateOfBirth ? new Date(user.dateOfBirth).toISOString().split('T')[0] : '',
      gender: user?.gender || '',
      address: {
        street: user?.address?.street || '',
        city: user?.address?.city || '',
        state: user?.address?.state || '',
        country: user?.address?.country || '',
        zipCode: user?.address?.zipCode || '',
      },
    },
  });

  const {
    register: registerPassword,
    handleSubmit: handlePasswordSubmit,
    formState: { errors: passwordErrors },
    reset: resetPassword,
  } = useForm({
    resolver: yupResolver(passwordSchema),
  });

  const handleProfileUpdate = async (data) => {
    try {
      setLoading(true);
      const result = await updateUser(data);

      if (result.success) {
        setIsEditing(false);
        toast.success('Profile updated successfully');
      }
    } catch (error) {
      console.error('Profile update error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordUpdate = async (data) => {
    try {
      setPasswordLoading(true);
      // This would call the auth API to update password
      // const result = await authAPI.updatePassword(data);

      toast.success('Password updated successfully');
      setShowPasswordModal(false);
      resetPassword();
    } catch (error) {
      console.error('Password update error:', error);
      toast.error('Failed to update password');
    } finally {
      setPasswordLoading(false);
    }
  };

  const handleImageUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await userAPI.uploadProfileImage(formData);
      const imageData = response.data.data;

      // Update user context with new image
      await updateUser({ profileImage: imageData.profileImage });
      toast.success('Profile image updated successfully');
    } catch (error) {
      console.error('Image upload error:', error);
      toast.error('Failed to upload image');
    }
  };

  const tabs = [
    { id: 'profile', label: 'Profile Information', icon: User },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'preferences', label: 'Preferences', icon: Settings },
    { id: 'notifications', label: 'Notifications', icon: Bell },
  ];

  return (
    <div className="min-h-screen bg-neutral-50">
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-neutral-900 mb-2">
            My Profile
          </h1>
          <p className="text-neutral-600">
            Manage your account settings and preferences
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <Card className="p-6">
              {/* Profile Picture */}
              <div className="text-center mb-6">
                <div className="relative inline-block">
                  <div className="w-24 h-24 bg-neutral-200 rounded-full overflow-hidden">
                    {user?.profileImage?.url ? (
                      <img
                        src={user.profileImage.url}
                        alt={user.fullName}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-neutral-500">
                        <User className="w-8 h-8" />
                      </div>
                    )}
                  </div>
                  <label className="absolute bottom-0 right-0 w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center cursor-pointer hover:bg-primary-700 transition-colors">
                    <Camera className="w-4 h-4 text-white" />
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                  </label>
                </div>
                <h3 className="text-lg font-semibold mt-3">
                  {user?.firstName} {user?.lastName}
                </h3>
                <p className="text-neutral-600">{user?.email}</p>
                <Badge variant="primary" className="mt-2">
                  {user?.role === 'tour-provider' ? 'Tour Provider' : 'Traveler'}
                </Badge>
              </div>

              {/* Navigation */}
              <nav className="space-y-1">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-neutral-600 hover:bg-neutral-100'
                    }`}
                  >
                    <tab.icon className="w-4 h-4 mr-3" />
                    {tab.label}
                  </button>
                ))}
              </nav>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Profile Information Tab */}
            {activeTab === 'profile' && (
              <Card className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold">Profile Information</h2>
                  {!isEditing ? (
                    <Button
                      variant="outline"
                      icon={Edit3}
                      onClick={() => setIsEditing(true)}
                    >
                      Edit Profile
                    </Button>
                  ) : (
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        icon={X}
                        onClick={() => {
                          setIsEditing(false);
                          resetProfile();
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="primary"
                        icon={Save}
                        onClick={handleProfileSubmit(handleProfileUpdate)}
                        loading={loading}
                      >
                        Save Changes
                      </Button>
                    </div>
                  )}
                </div>

                <form onSubmit={handleProfileSubmit(handleProfileUpdate)}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Basic Information */}
                    <div className="md:col-span-2">
                      <h3 className="text-lg font-medium mb-4">Basic Information</h3>
                    </div>

                    <Input
                      label="First Name"
                      icon={User}
                      disabled={!isEditing}
                      error={profileErrors.firstName?.message}
                      {...registerProfile('firstName')}
                      required
                    />

                    <Input
                      label="Last Name"
                      icon={User}
                      disabled={!isEditing}
                      error={profileErrors.lastName?.message}
                      {...registerProfile('lastName')}
                      required
                    />

                    <Input
                      label="Email Address"
                      type="email"
                      icon={Mail}
                      disabled={!isEditing}
                      error={profileErrors.email?.message}
                      {...registerProfile('email')}
                      required
                    />

                    <Input
                      label="Phone Number"
                      type="tel"
                      icon={Phone}
                      disabled={!isEditing}
                      error={profileErrors.phone?.message}
                      {...registerProfile('phone')}
                    />

                    <Input
                      label="Date of Birth"
                      type="date"
                      icon={Calendar}
                      disabled={!isEditing}
                      error={profileErrors.dateOfBirth?.message}
                      {...registerProfile('dateOfBirth')}
                    />

                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-1">
                        Gender
                      </label>
                      <select
                        className="input w-full"
                        disabled={!isEditing}
                        {...registerProfile('gender')}
                      >
                        <option value="">Select Gender</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                      </select>
                    </div>

                    {/* Address Information */}
                    <div className="md:col-span-2 mt-6">
                      <h3 className="text-lg font-medium mb-4">Address Information</h3>
                    </div>

                    <div className="md:col-span-2">
                      <Input
                        label="Street Address"
                        icon={MapPin}
                        disabled={!isEditing}
                        error={profileErrors.address?.street?.message}
                        {...registerProfile('address.street')}
                      />
                    </div>

                    <Input
                      label="City"
                      disabled={!isEditing}
                      error={profileErrors.address?.city?.message}
                      {...registerProfile('address.city')}
                    />

                    <Input
                      label="State/Province"
                      disabled={!isEditing}
                      error={profileErrors.address?.state?.message}
                      {...registerProfile('address.state')}
                    />

                    <Input
                      label="Country"
                      disabled={!isEditing}
                      error={profileErrors.address?.country?.message}
                      {...registerProfile('address.country')}
                    />

                    <Input
                      label="ZIP/Postal Code"
                      disabled={!isEditing}
                      error={profileErrors.address?.zipCode?.message}
                      {...registerProfile('address.zipCode')}
                    />
                  </div>
                </form>
              </Card>
            )}

            {/* Security Tab */}
            {activeTab === 'security' && (
              <Card className="p-6">
                <h2 className="text-xl font-semibold mb-6">Security Settings</h2>

                <div className="space-y-6">
                  {/* Password Section */}
                  <div className="flex items-center justify-between p-4 border border-neutral-200 rounded-lg">
                    <div>
                      <h3 className="font-medium">Password</h3>
                      <p className="text-sm text-neutral-600">
                        Last updated 30 days ago
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      icon={Lock}
                      onClick={() => setShowPasswordModal(true)}
                    >
                      Change Password
                    </Button>
                  </div>

                  {/* Two-Factor Authentication */}
                  <div className="flex items-center justify-between p-4 border border-neutral-200 rounded-lg">
                    <div>
                      <h3 className="font-medium">Two-Factor Authentication</h3>
                      <p className="text-sm text-neutral-600">
                        Add an extra layer of security to your account
                      </p>
                    </div>
                    <Button variant="outline" disabled>
                      Enable 2FA
                    </Button>
                  </div>

                  {/* Login Sessions */}
                  <div className="p-4 border border-neutral-200 rounded-lg">
                    <h3 className="font-medium mb-3">Active Sessions</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium">Current Session</p>
                          <p className="text-xs text-neutral-500">
                            Chrome on Windows • Last active now
                          </p>
                        </div>
                        <Badge variant="success">Active</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            )}

            {/* Preferences Tab */}
            {activeTab === 'preferences' && (
              <Card className="p-6">
                <h2 className="text-xl font-semibold mb-6">Travel Preferences</h2>
                <div className="text-center py-12 text-neutral-500">
                  <Settings className="w-12 h-12 mx-auto mb-4 text-neutral-300" />
                  <p>Travel preferences coming soon...</p>
                </div>
              </Card>
            )}

            {/* Notifications Tab */}
            {activeTab === 'notifications' && (
              <Card className="p-6">
                <h2 className="text-xl font-semibold mb-6">Notification Settings</h2>
                <div className="text-center py-12 text-neutral-500">
                  <Bell className="w-12 h-12 mx-auto mb-4 text-neutral-300" />
                  <p>Notification settings coming soon...</p>
                </div>
              </Card>
            )}
          </div>
        </div>
      </div>

      {/* Password Change Modal */}
      <Modal
        isOpen={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
        title="Change Password"
        size="default"
      >
        <form onSubmit={handlePasswordSubmit(handlePasswordUpdate)}>
          <div className="space-y-4">
            <Input
              label="Current Password"
              type="password"
              icon={Lock}
              error={passwordErrors.currentPassword?.message}
              {...registerPassword('currentPassword')}
              required
            />

            <Input
              label="New Password"
              type="password"
              icon={Lock}
              error={passwordErrors.newPassword?.message}
              {...registerPassword('newPassword')}
              required
            />

            <Input
              label="Confirm New Password"
              type="password"
              icon={Lock}
              error={passwordErrors.confirmPassword?.message}
              {...registerPassword('confirmPassword')}
              required
            />
          </div>

          <Modal.Footer>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowPasswordModal(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={passwordLoading}
            >
              Update Password
            </Button>
          </Modal.Footer>
        </form>
      </Modal>
    </div>
  );
};

export default ProfilePage;
