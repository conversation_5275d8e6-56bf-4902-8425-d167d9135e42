const Booking = require('../models/Booking');

// Simulate payment processing (replace with actual payment gateway integration)
const processPayment = async (bookingId, paymentDetails) => {
  try {
    const booking = await Booking.findById(bookingId);
    
    if (!booking) {
      throw new Error('Booking not found');
    }

    // Simulate payment processing delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Simulate payment success/failure (90% success rate)
    const isSuccess = Math.random() > 0.1;

    if (isSuccess) {
      // Update booking payment status
      booking.payment.status = 'completed';
      booking.payment.method = paymentDetails.method;
      booking.payment.transactionId = generateTransactionId();
      booking.payment.paidAt = new Date();
      booking.status = 'confirmed';

      await booking.save();

      return {
        success: true,
        transactionId: booking.payment.transactionId,
        message: 'Payment processed successfully'
      };
    } else {
      // Payment failed
      booking.payment.status = 'failed';
      await booking.save();

      return {
        success: false,
        message: 'Payment processing failed. Please try again.'
      };
    }
  } catch (error) {
    console.error('Payment processing error:', error);
    return {
      success: false,
      message: error.message || 'Payment processing failed'
    };
  }
};

// Process refund
const processRefund = async (bookingId, refundAmount, reason) => {
  try {
    const booking = await Booking.findById(bookingId);
    
    if (!booking) {
      throw new Error('Booking not found');
    }

    if (booking.payment.status !== 'completed') {
      throw new Error('Cannot refund unpaid booking');
    }

    // Calculate refund amount based on cancellation policy
    const calculatedRefund = calculateRefundAmount(booking, refundAmount);

    // Simulate refund processing
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Update booking
    booking.payment.refundAmount = calculatedRefund;
    booking.payment.refundReason = reason;
    booking.payment.refundedAt = new Date();
    
    if (calculatedRefund >= booking.pricing.totalAmount) {
      booking.payment.status = 'refunded';
    } else {
      booking.payment.status = 'partially-refunded';
    }

    await booking.save();

    return {
      success: true,
      refundAmount: calculatedRefund,
      message: 'Refund processed successfully'
    };
  } catch (error) {
    console.error('Refund processing error:', error);
    return {
      success: false,
      message: error.message || 'Refund processing failed'
    };
  }
};

// Calculate refund amount based on cancellation policy
const calculateRefundAmount = (booking, requestedAmount) => {
  const totalAmount = booking.pricing.totalAmount;
  const daysUntilTour = booking.daysUntilTour;

  let refundPercentage = 0;

  // Refund policy based on days until tour
  if (daysUntilTour >= 7) {
    refundPercentage = 100; // Full refund
  } else if (daysUntilTour >= 3) {
    refundPercentage = 75; // 75% refund
  } else if (daysUntilTour >= 1) {
    refundPercentage = 50; // 50% refund
  } else {
    refundPercentage = 0; // No refund
  }

  const maxRefund = (totalAmount * refundPercentage) / 100;
  
  // Return the lesser of requested amount or maximum allowed refund
  return Math.min(requestedAmount || maxRefund, maxRefund);
};

// Generate transaction ID
const generateTransactionId = () => {
  const timestamp = Date.now().toString();
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `TXN${timestamp}${random}`;
};

// Validate payment details
const validatePaymentDetails = (paymentDetails) => {
  const { method, cardNumber, expiryDate, cvv, cardholderName } = paymentDetails;

  if (!method) {
    return { isValid: false, message: 'Payment method is required' };
  }

  if (method === 'credit-card' || method === 'debit-card') {
    if (!cardNumber || cardNumber.length < 13 || cardNumber.length > 19) {
      return { isValid: false, message: 'Invalid card number' };
    }

    if (!expiryDate || !/^\d{2}\/\d{2}$/.test(expiryDate)) {
      return { isValid: false, message: 'Invalid expiry date format (MM/YY)' };
    }

    if (!cvv || cvv.length < 3 || cvv.length > 4) {
      return { isValid: false, message: 'Invalid CVV' };
    }

    if (!cardholderName || cardholderName.trim().length < 2) {
      return { isValid: false, message: 'Cardholder name is required' };
    }

    // Check if card is expired
    const [month, year] = expiryDate.split('/');
    const expiry = new Date(2000 + parseInt(year), parseInt(month) - 1);
    const now = new Date();
    
    if (expiry < now) {
      return { isValid: false, message: 'Card has expired' };
    }
  }

  return { isValid: true };
};

// Get payment methods
const getPaymentMethods = () => {
  return [
    {
      id: 'credit-card',
      name: 'Credit Card',
      description: 'Visa, MasterCard, American Express',
      processingFee: 2.9,
      isActive: true
    },
    {
      id: 'debit-card',
      name: 'Debit Card',
      description: 'Bank debit cards',
      processingFee: 1.9,
      isActive: true
    },
    {
      id: 'paypal',
      name: 'PayPal',
      description: 'Pay with your PayPal account',
      processingFee: 3.4,
      isActive: true
    },
    {
      id: 'bank-transfer',
      name: 'Bank Transfer',
      description: 'Direct bank transfer',
      processingFee: 0,
      isActive: true
    }
  ];
};

// Create payment intent (for future Stripe integration)
const createPaymentIntent = async (bookingId, amount, currency = 'USD') => {
  try {
    // This would integrate with Stripe or other payment processors
    // For now, return a mock payment intent
    
    return {
      id: `pi_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      amount: amount * 100, // Convert to cents
      currency: currency.toLowerCase(),
      status: 'requires_payment_method',
      client_secret: `pi_${Date.now()}_secret_${Math.random().toString(36).substr(2, 9)}`
    };
  } catch (error) {
    console.error('Create payment intent error:', error);
    throw new Error('Failed to create payment intent');
  }
};

module.exports = {
  processPayment,
  processRefund,
  calculateRefundAmount,
  validatePaymentDetails,
  getPaymentMethods,
  createPaymentIntent,
  generateTransactionId
};
