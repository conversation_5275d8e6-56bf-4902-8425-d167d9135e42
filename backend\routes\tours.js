const express = require('express');
const router = express.Router();

// Import review routes
const reviewRouter = require('./reviews');

// Import controllers
const {
  getAllTours,
  getTour,
  createTour,
  updateTour,
  deleteTour,
  uploadTourImages,
  deleteTourImage,
  getFeaturedTours,
  getTourStats,
  getRecommendations,
  getSimilarToursController,
  getTrendingToursController
} = require('../controllers/tourController');

const { protect, authorize, optionalAuth } = require('../middleware/auth');
const { uploadTourImages: uploadMiddleware, handleMulterError } = require('../middleware/upload');

// Nested routes
router.use('/:tourId/reviews', reviewRouter);

// Public routes
router.get('/featured', getFeaturedTours);
router.get('/trending', getTrendingToursController);
router.get('/stats', getTourStats);
router.get('/', optionalAuth, getAllTours);
router.get('/:id', optionalAuth, getTour);
router.get('/:id/similar', getSimilarToursController);

// Protected routes - require authentication
router.use(protect);

// User routes (authenticated)
router.get('/recommendations', getRecommendations);

// Tour provider and admin routes
router.post('/', authorize('tour-provider', 'admin'), createTour);
router.put('/:id', authorize('tour-provider', 'admin'), updateTour);
router.delete('/:id', authorize('tour-provider', 'admin'), deleteTour);

// Image management routes
router.post('/:id/images',
  authorize('tour-provider', 'admin'),
  uploadMiddleware,
  handleMulterError,
  uploadTourImages
);
router.delete('/:id/images/:imageId',
  authorize('tour-provider', 'admin'),
  deleteTourImage
);

module.exports = router;
