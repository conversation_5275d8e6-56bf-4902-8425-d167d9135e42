import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Search, MapPin, Star, Users, Calendar, ArrowRight } from 'lucide-react';
import { tourAPI } from '../services/api';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import Badge from '../components/ui/Badge';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import toast from 'react-hot-toast';

const HomePage = () => {
  const [featuredTours, setFeaturedTours] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchFeaturedTours();
  }, []);

  const fetchFeaturedTours = async () => {
    try {
      setLoading(true);
      const response = await tourAPI.getFeaturedTours({ limit: 3 });
      setFeaturedTours(response.data.data);
    } catch (error) {
      console.error('Error fetching featured tours:', error);
      toast.error('Failed to load featured tours');
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const stats = [
    { label: 'Happy Travelers', value: '50,000+' },
    { label: 'Tour Destinations', value: '200+' },
    { label: 'Years Experience', value: '15+' },
    { label: 'Tour Packages', value: '1,000+' },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center bg-gradient-to-r from-primary-600 to-secondary-600">
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: 'url(https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=1920&h=1080&fit=crop)'
          }}
        ></div>
        
        <div className="relative z-10 text-center text-white max-w-4xl mx-auto px-4">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 animate-fade-in">
            Discover Your Next
            <span className="block text-gradient bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
              Adventure
            </span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-neutral-200 animate-slide-up">
            Explore amazing destinations with our curated travel experiences
          </p>
          
          {/* Search Bar */}
          <div className="bg-white rounded-2xl p-6 shadow-2xl max-w-4xl mx-auto animate-slide-up">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <MapPin className="absolute left-3 top-3 w-5 h-5 text-neutral-400" />
                <input
                  type="text"
                  placeholder="Where to?"
                  className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-neutral-900"
                />
              </div>
              <div className="relative">
                <Calendar className="absolute left-3 top-3 w-5 h-5 text-neutral-400" />
                <input
                  type="date"
                  className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-neutral-900"
                />
              </div>
              <div className="relative">
                <Users className="absolute left-3 top-3 w-5 h-5 text-neutral-400" />
                <select className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-neutral-900">
                  <option>1 Guest</option>
                  <option>2 Guests</option>
                  <option>3 Guests</option>
                  <option>4+ Guests</option>
                </select>
              </div>
              <Button variant="primary" size="lg" icon={Search} className="w-full">
                Search Tours
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="container-custom">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-primary-600 mb-2">
                  {stat.value}
                </div>
                <div className="text-neutral-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Tours Section */}
      <section className="py-20 bg-neutral-50">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-neutral-900 mb-4">
              Featured Tours
            </h2>
            <p className="text-xl text-neutral-600 max-w-2xl mx-auto">
              Discover our most popular destinations and experiences, carefully curated for unforgettable adventures.
            </p>
          </div>

          {loading ? (
            <div className="flex justify-center py-12">
              <LoadingSpinner size="lg" />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredTours.map((tour) => (
                <Card key={tour._id} hover className="overflow-hidden">
                  <div className="relative">
                    <img
                      src={tour.images?.find(img => img.isMain)?.url || tour.images?.[0]?.url || '/placeholder-tour.jpg'}
                      alt={tour.name}
                      className="w-full h-48 object-cover"
                    />
                    {tour.isFeatured && (
                      <Badge variant="warning" className="absolute top-3 left-3">
                        Featured
                      </Badge>
                    )}
                    <Badge variant="primary" className="absolute top-3 right-3">
                      {tour.category}
                    </Badge>
                  </div>

                  <Card.Content className="p-6">
                    <div className="flex items-center text-sm text-neutral-500 mb-2">
                      <MapPin className="w-4 h-4 mr-1" />
                      {tour.destination.city}, {tour.destination.country}
                    </div>

                    <h3 className="text-xl font-semibold text-neutral-900 mb-3">
                      {tour.name}
                    </h3>

                    <div className="flex items-center mb-4">
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="ml-1 text-sm font-medium">
                          {tour.ratingsAverage ? tour.ratingsAverage.toFixed(1) : 'New'}
                        </span>
                      </div>
                      {tour.ratingsQuantity > 0 && (
                        <span className="text-sm text-neutral-500 ml-2">
                          ({tour.ratingsQuantity} reviews)
                        </span>
                      )}
                      <span className="text-sm text-neutral-500 ml-auto">
                        {tour.duration.days} days
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-2xl font-bold text-primary-600">
                          {formatPrice(tour.price, tour.currency)}
                        </span>
                        {tour.originalPrice && tour.originalPrice > tour.price && (
                          <span className="text-sm text-neutral-500 line-through">
                            {formatPrice(tour.originalPrice, tour.currency)}
                          </span>
                        )}
                      </div>
                      <Link to={`/tours/${tour.slug || tour._id}`}>
                        <Button variant="primary" size="sm">
                          View Details
                        </Button>
                      </Link>
                    </div>
                  </Card.Content>
                </Card>
              ))}
            </div>
          )}

          <div className="text-center mt-12">
            <Link to="/tours">
              <Button variant="outline" size="lg" icon={ArrowRight} iconPosition="right">
                View All Tours
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-primary text-white">
        <div className="container-custom text-center">
          <h2 className="text-4xl font-bold mb-4">
            Ready to Start Your Journey?
          </h2>
          <p className="text-xl mb-8 text-neutral-200 max-w-2xl mx-auto">
            Join thousands of travelers who have discovered amazing destinations with Wanderlust.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/tours">
              <Button variant="secondary" size="lg">
                Explore Tours
              </Button>
            </Link>
            <Link to="/auth/register">
              <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-primary-600">
                Sign Up Free
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
